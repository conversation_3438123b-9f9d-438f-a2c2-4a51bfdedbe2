(function(an,fe,on){"use strict";function or(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function ur(e,t,n){return t&&or(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}/*!
 * Splide.js
 * Version  : 4.1.4
 * License  : MIT
 * Copyright: 2022 Naotoshi Fujita
 */var un="(prefers-reduced-motion: reduce)",De=1,sr=2,we=3,Me=4,He=5,dt=6,gt=7,cr={CREATED:De,MOUNTED:sr,IDLE:we,MOVING:Me,SCROLLING:He,DRAGGING:dt,DESTROYED:gt};function ve(e){e.length=0}function me(e,t,n){return Array.prototype.slice.call(e,t,n)}function V(e){return e.bind.apply(e,[null].concat(me(arguments,1)))}var sn=setTimeout,Pt=function(){};function cn(e){return requestAnimationFrame(e)}function Et(e,t){return typeof t===e}function Ke(e){return!wt(e)&&Et("object",e)}var Dt=Array.isArray,ln=V(Et,"function"),_e=V(Et,"string"),We=V(Et,"undefined");function wt(e){return e===null}function fn(e){try{return e instanceof(e.ownerDocument.defaultView||window).HTMLElement}catch{return!1}}function Xe(e){return Dt(e)?e:[e]}function te(e,t){Xe(e).forEach(t)}function Mt(e,t){return e.indexOf(t)>-1}function ht(e,t){return e.push.apply(e,Xe(t)),e}function de(e,t,n){e&&te(t,function(r){r&&e.classList[n?"add":"remove"](r)})}function se(e,t){de(e,_e(t)?t.split(" "):t,!0)}function qe(e,t){te(t,e.appendChild.bind(e))}function xt(e,t){te(e,function(n){var r=(t||n).parentNode;r&&r.insertBefore(n,t)})}function je(e,t){return fn(e)&&(e.msMatchesSelector||e.matches).call(e,t)}function vn(e,t){var n=e?me(e.children):[];return t?n.filter(function(r){return je(r,t)}):n}function Ze(e,t){return t?vn(e,t)[0]:e.firstElementChild}var Je=Object.keys;function Le(e,t,n){return e&&(n?Je(e).reverse():Je(e)).forEach(function(r){r!=="__proto__"&&t(e[r],r)}),e}function Qe(e){return me(arguments,1).forEach(function(t){Le(t,function(n,r){e[r]=t[r]})}),e}function Ae(e){return me(arguments,1).forEach(function(t){Le(t,function(n,r){Dt(n)?e[r]=n.slice():Ke(n)?e[r]=Ae({},Ke(e[r])?e[r]:{},n):e[r]=n})}),e}function dn(e,t){te(t||Je(e),function(n){delete e[n]})}function ce(e,t){te(e,function(n){te(t,function(r){n&&n.removeAttribute(r)})})}function M(e,t,n){Ke(t)?Le(t,function(r,i){M(e,i,r)}):te(e,function(r){wt(n)||n===""?ce(r,t):r.setAttribute(t,String(n))})}function xe(e,t,n){var r=document.createElement(e);return t&&(_e(t)?se(r,t):M(r,t)),n&&qe(n,r),r}function ie(e,t,n){if(We(n))return getComputedStyle(e)[t];wt(n)||(e.style[t]=""+n)}function et(e,t){ie(e,"display",t)}function gn(e){e.setActive&&e.setActive()||e.focus({preventScroll:!0})}function ae(e,t){return e.getAttribute(t)}function En(e,t){return e&&e.classList.contains(t)}function ne(e){return e.getBoundingClientRect()}function Ne(e){te(e,function(t){t&&t.parentNode&&t.parentNode.removeChild(t)})}function hn(e){return Ze(new DOMParser().parseFromString(e,"text/html").body)}function ge(e,t){e.preventDefault(),t&&(e.stopPropagation(),e.stopImmediatePropagation())}function Tn(e,t){return e&&e.querySelector(t)}function Ft(e,t){return t?me(e.querySelectorAll(t)):[]}function Ee(e,t){de(e,t,!1)}function Gt(e){return e.timeStamp}function be(e){return _e(e)?e:e?e+"px":""}var tt="splide",kt="data-"+tt;function nt(e,t){if(!e)throw new Error("["+tt+"] "+(t||""))}var Se=Math.min,Tt=Math.max,mt=Math.floor,rt=Math.ceil,Z=Math.abs;function mn(e,t,n){return Z(e-t)<n}function _t(e,t,n,r){var i=Se(t,n),u=Tt(t,n);return r?i<e&&e<u:i<=e&&e<=u}function Fe(e,t,n){var r=Se(t,n),i=Tt(t,n);return Se(Tt(r,e),i)}function Ut(e){return+(e>0)-+(e<0)}function Vt(e,t){return te(t,function(n){e=e.replace("%s",""+n)}),e}function Bt(e){return e<10?"0"+e:""+e}var _n={};function lr(e){return""+e+Bt(_n[e]=(_n[e]||0)+1)}function An(){var e=[];function t(a,o,s,l){i(a,o,function(c,E,d){var g="addEventListener"in c,f=g?c.removeEventListener.bind(c,E,s,l):c.removeListener.bind(c,s);g?c.addEventListener(E,s,l):c.addListener(s),e.push([c,E,d,s,f])})}function n(a,o,s){i(a,o,function(l,c,E){e=e.filter(function(d){return d[0]===l&&d[1]===c&&d[2]===E&&(!s||d[3]===s)?(d[4](),!1):!0})})}function r(a,o,s){var l,c=!0;return typeof CustomEvent=="function"?l=new CustomEvent(o,{bubbles:c,detail:s}):(l=document.createEvent("CustomEvent"),l.initCustomEvent(o,c,!1,s)),a.dispatchEvent(l),l}function i(a,o,s){te(a,function(l){l&&te(o,function(c){c.split(" ").forEach(function(E){var d=E.split(".");s(l,d[0],d[1])})})})}function u(){e.forEach(function(a){a[4]()}),ve(e)}return{bind:t,unbind:n,dispatch:r,destroy:u}}var Oe="mounted",Sn="ready",pe="move",it="moved",pn="click",fr="active",vr="inactive",dr="visible",gr="hidden",W="refresh",J="updated",at="resize",zt="resized",Er="drag",hr="dragging",Tr="dragged",Yt="scroll",Ge="scrolled",mr="overflow",yn="destroy",_r="arrows:mounted",Ar="arrows:updated",Sr="pagination:mounted",pr="pagination:updated",In="navigation:mounted",Ln="autoplay:play",yr="autoplay:playing",Nn="autoplay:pause",bn="lazyload:loaded",On="sk",Rn="sh",At="ei";function Y(e){var t=e?e.event.bus:document.createDocumentFragment(),n=An();function r(u,a){n.bind(t,Xe(u).join(" "),function(o){a.apply(a,Dt(o.detail)?o.detail:[])})}function i(u){n.dispatch(t,u,me(arguments,1))}return e&&e.event.on(yn,n.destroy),Qe(n,{bus:t,on:r,off:V(n.unbind,t),emit:i})}function St(e,t,n,r){var i=Date.now,u,a=0,o,s=!0,l=0;function c(){if(!s){if(a=e?Se((i()-u)/e,1):1,n&&n(a),a>=1&&(t(),u=i(),r&&++l>=r))return d();o=cn(c)}}function E(m){m||f(),u=i()-(m?a*e:0),s=!1,o=cn(c)}function d(){s=!0}function g(){u=i(),a=0,n&&n(a)}function f(){o&&cancelAnimationFrame(o),a=0,o=0,s=!0}function v(m){e=m}function _(){return s}return{start:E,rewind:g,pause:d,cancel:f,set:v,isPaused:_}}function Ir(e){var t=e;function n(i){t=i}function r(i){return Mt(Xe(i),t)}return{set:n,is:r}}function Lr(e,t){var n=St(0,e,null,1);return function(){n.isPaused()&&n.start()}}function Nr(e,t,n){var r=e.state,i=n.breakpoints||{},u=n.reducedMotion||{},a=An(),o=[];function s(){var f=n.mediaQuery==="min";Je(i).sort(function(v,_){return f?+v-+_:+_-+v}).forEach(function(v){c(i[v],"("+(f?"min":"max")+"-width:"+v+"px)")}),c(u,un),E()}function l(f){f&&a.destroy()}function c(f,v){var _=matchMedia(v);a.bind(_,"change",E),o.push([f,_])}function E(){var f=r.is(gt),v=n.direction,_=o.reduce(function(m,T){return Ae(m,T[1].matches?T[0]:{})},{});dn(n),g(_),n.destroy?e.destroy(n.destroy==="completely"):f?(l(!0),e.mount()):v!==n.direction&&e.refresh()}function d(f){matchMedia(un).matches&&(f?Ae(n,u):dn(n,Je(u)))}function g(f,v,_){Ae(n,f),v&&Ae(Object.getPrototypeOf(n),f),(_||!r.is(De))&&e.emit(J,n)}return{setup:s,destroy:l,reduce:d,set:g}}var pt="Arrow",yt=pt+"Left",It=pt+"Right",Cn=pt+"Up",Pn=pt+"Down",Dn="rtl",Lt="ttb",$t={width:["height"],left:["top","right"],right:["bottom","left"],x:["y"],X:["Y"],Y:["X"],ArrowLeft:[Cn,It],ArrowRight:[Pn,yt]};function br(e,t,n){function r(u,a,o){o=o||n.direction;var s=o===Dn&&!a?1:o===Lt?0:-1;return $t[u]&&$t[u][s]||u.replace(/width|left|right/i,function(l,c){var E=$t[l.toLowerCase()][s]||l;return c>0?E.charAt(0).toUpperCase()+E.slice(1):E})}function i(u){return u*(n.direction===Dn?1:-1)}return{resolve:r,orient:i}}var he="role",ke="tabindex",Or="disabled",oe="aria-",ot=oe+"controls",wn=oe+"current",Mn=oe+"selected",re=oe+"label",Ht=oe+"labelledby",xn=oe+"hidden",Kt=oe+"orientation",ut=oe+"roledescription",Fn=oe+"live",Gn=oe+"busy",kn=oe+"atomic",Wt=[he,ke,Or,ot,wn,re,Ht,xn,Kt,ut],le=tt+"__",ye="is-",Xt=tt,Un=le+"track",Rr=le+"list",Nt=le+"slide",Vn=Nt+"--clone",Cr=Nt+"__container",qt=le+"arrows",bt=le+"arrow",Bn=bt+"--prev",zn=bt+"--next",Ot=le+"pagination",Yn=Ot+"__page",Pr=le+"progress",Dr=Pr+"__bar",wr=le+"toggle",Mr=le+"spinner",xr=le+"sr",Fr=ye+"initialized",Re=ye+"active",$n=ye+"prev",Hn=ye+"next",jt=ye+"visible",Zt=ye+"loading",Kn=ye+"focus-in",Wn=ye+"overflow",Gr=[Re,jt,$n,Hn,Zt,Kn,Wn],kr={slide:Nt,clone:Vn,arrows:qt,arrow:bt,prev:Bn,next:zn,pagination:Ot,page:Yn,spinner:Mr};function Ur(e,t){if(ln(e.closest))return e.closest(t);for(var n=e;n&&n.nodeType===1&&!je(n,t);)n=n.parentElement;return n}var Vr=5,Xn=200,qn="touchstart mousedown",Jt="touchmove mousemove",Qt="touchend touchcancel mouseup click";function Br(e,t,n){var r=Y(e),i=r.on,u=r.bind,a=e.root,o=n.i18n,s={},l=[],c=[],E=[],d,g,f;function v(){h(),D(),T()}function _(){i(W,m),i(W,v),i(J,T),u(document,qn+" keydown",function(S){f=S.type==="keydown"},{capture:!0}),u(a,"focusin",function(){de(a,Kn,!!f)})}function m(S){var O=Wt.concat("style");ve(l),Ee(a,c),Ee(d,E),ce([d,g],O),ce(a,S?O:["style",ut])}function T(){Ee(a,c),Ee(d,E),c=x(Xt),E=x(Un),se(a,c),se(d,E),M(a,re,n.label),M(a,Ht,n.labelledby)}function h(){d=b("."+Un),g=Ze(d,"."+Rr),nt(d&&g,"A track/list element is missing."),ht(l,vn(g,"."+Nt+":not(."+Vn+")")),Le({arrows:qt,pagination:Ot,prev:Bn,next:zn,bar:Dr,toggle:wr},function(S,O){s[O]=b("."+S)}),Qe(s,{root:a,track:d,list:g,slides:l})}function D(){var S=a.id||lr(tt),O=n.role;a.id=S,d.id=d.id||S+"-track",g.id=g.id||S+"-list",!ae(a,he)&&a.tagName!=="SECTION"&&O&&M(a,he,O),M(a,ut,o.carousel),M(g,he,"presentation")}function b(S){var O=Tn(a,S);return O&&Ur(O,"."+Xt)===a?O:void 0}function x(S){return[S+"--"+n.type,S+"--"+n.direction,n.drag&&S+"--draggable",n.isNavigation&&S+"--nav",S===Xt&&Re]}return Qe(s,{setup:v,mount:_,destroy:m})}var Ue="slide",Ve="loop",st="fade";function zr(e,t,n,r){var i=Y(e),u=i.on,a=i.emit,o=i.bind,s=e.Components,l=e.root,c=e.options,E=c.isNavigation,d=c.updateOnMove,g=c.i18n,f=c.pagination,v=c.slideFocus,_=s.Direction.resolve,m=ae(r,"style"),T=ae(r,re),h=n>-1,D=Ze(r,"."+Cr),b;function x(){h||(r.id=l.id+"-slide"+Bt(t+1),M(r,he,f?"tabpanel":"group"),M(r,ut,g.slide),M(r,re,T||Vt(g.slideLabel,[t+1,e.length]))),S()}function S(){o(r,"click",V(a,pn,w)),o(r,"keydown",V(a,On,w)),u([it,Rn,Ge],y),u(In,k),d&&u(pe,P)}function O(){b=!0,i.destroy(),Ee(r,Gr),ce(r,Wt),M(r,"style",m),M(r,re,T||"")}function k(){var C=e.splides.map(function(p){var R=p.splide.Components.Slides.getAt(t);return R?R.slide.id:""}).join(" ");M(r,re,Vt(g.slideX,(h?n:t)+1)),M(r,ot,C),M(r,he,v?"button":""),v&&ce(r,ut)}function P(){b||y()}function y(){if(!b){var C=e.index;I(),L(),de(r,$n,t===C-1),de(r,Hn,t===C+1)}}function I(){var C=G();C!==En(r,Re)&&(de(r,Re,C),M(r,wn,E&&C||""),a(C?fr:vr,w))}function L(){var C=H(),p=!C&&(!G()||h);if(e.state.is([Me,He])||M(r,xn,p||""),M(Ft(r,c.focusableNodes||""),ke,p?-1:""),v&&M(r,ke,p?-1:0),C!==En(r,jt)&&(de(r,jt,C),a(C?dr:gr,w)),!C&&document.activeElement===r){var R=s.Slides.getAt(e.index);R&&gn(R.slide)}}function F(C,p,R){ie(R&&D||r,C,p)}function G(){var C=e.index;return C===t||c.cloneStatus&&C===n}function H(){if(e.is(st))return G();var C=ne(s.Elements.track),p=ne(r),R=_("left",!0),U=_("right",!0);return mt(C[R])<=rt(p[R])&&mt(p[U])<=rt(C[U])}function $(C,p){var R=Z(C-t);return!h&&(c.rewind||e.is(Ve))&&(R=Se(R,e.length-R)),R<=p}var w={index:t,slideIndex:n,slide:r,container:D,isClone:h,mount:x,destroy:O,update:y,style:F,isWithin:$};return w}function Yr(e,t,n){var r=Y(e),i=r.on,u=r.emit,a=r.bind,o=t.Elements,s=o.slides,l=o.list,c=[];function E(){d(),i(W,g),i(W,d)}function d(){s.forEach(function(y,I){v(y,I,-1)})}function g(){b(function(y){y.destroy()}),ve(c)}function f(){b(function(y){y.update()})}function v(y,I,L){var F=zr(e,I,L,y);F.mount(),c.push(F),c.sort(function(G,H){return G.index-H.index})}function _(y){return y?x(function(I){return!I.isClone}):c}function m(y){var I=t.Controller,L=I.toIndex(y),F=I.hasFocus()?1:n.perPage;return x(function(G){return _t(G.index,L,L+F-1)})}function T(y){return x(y)[0]}function h(y,I){te(y,function(L){if(_e(L)&&(L=hn(L)),fn(L)){var F=s[I];F?xt(L,F):qe(l,L),se(L,n.classes.slide),O(L,V(u,at))}}),u(W)}function D(y){Ne(x(y).map(function(I){return I.slide})),u(W)}function b(y,I){_(I).forEach(y)}function x(y){return c.filter(ln(y)?y:function(I){return _e(y)?je(I.slide,y):Mt(Xe(y),I.index)})}function S(y,I,L){b(function(F){F.style(y,I,L)})}function O(y,I){var L=Ft(y,"img"),F=L.length;F?L.forEach(function(G){a(G,"load error",function(){--F||I()})}):I()}function k(y){return y?s.length:c.length}function P(){return c.length>n.perPage}return{mount:E,destroy:g,update:f,register:v,get:_,getIn:m,getAt:T,add:h,remove:D,forEach:b,filter:x,style:S,getLength:k,isEnough:P}}function $r(e,t,n){var r=Y(e),i=r.on,u=r.bind,a=r.emit,o=t.Slides,s=t.Direction.resolve,l=t.Elements,c=l.root,E=l.track,d=l.list,g=o.getAt,f=o.style,v,_,m;function T(){h(),u(window,"resize load",Lr(V(a,at))),i([J,W],h),i(at,D)}function h(){v=n.direction===Lt,ie(c,"maxWidth",be(n.width)),ie(E,s("paddingLeft"),b(!1)),ie(E,s("paddingRight"),b(!0)),D(!0)}function D(w){var C=ne(c);(w||_.width!==C.width||_.height!==C.height)&&(ie(E,"height",x()),f(s("marginRight"),be(n.gap)),f("width",O()),f("height",k(),!0),_=C,a(zt),m!==(m=$())&&(de(c,Wn,m),a(mr,m)))}function b(w){var C=n.padding,p=s(w?"right":"left");return C&&be(C[p]||(Ke(C)?0:C))||"0px"}function x(){var w="";return v&&(w=S(),nt(w,"height or heightRatio is missing."),w="calc("+w+" - "+b(!1)+" - "+b(!0)+")"),w}function S(){return be(n.height||ne(d).width*n.heightRatio)}function O(){return n.autoWidth?null:be(n.fixedWidth)||(v?"":P())}function k(){return be(n.fixedHeight)||(v?n.autoHeight?null:P():S())}function P(){var w=be(n.gap);return"calc((100%"+(w&&" + "+w)+")/"+(n.perPage||1)+(w&&" - "+w)+")"}function y(){return ne(d)[s("width")]}function I(w,C){var p=g(w||0);return p?ne(p.slide)[s("width")]+(C?0:G()):0}function L(w,C){var p=g(w);if(p){var R=ne(p.slide)[s("right")],U=ne(d)[s("left")];return Z(R-U)+(C?0:G())}return 0}function F(w){return L(e.length-1)-L(0)+I(0,w)}function G(){var w=g(0);return w&&parseFloat(ie(w.slide,s("marginRight")))||0}function H(w){return parseFloat(ie(E,s("padding"+(w?"Right":"Left"))))||0}function $(){return e.is(st)||F(!0)>y()}return{mount:T,resize:D,listSize:y,slideSize:I,sliderSize:F,totalSize:L,getPadding:H,isOverflow:$}}var Hr=2;function Kr(e,t,n){var r=Y(e),i=r.on,u=t.Elements,a=t.Slides,o=t.Direction.resolve,s=[],l;function c(){i(W,E),i([J,at],g),(l=_())&&(f(l),t.Layout.resize(!0))}function E(){d(),c()}function d(){Ne(s),ve(s),r.destroy()}function g(){var m=_();l!==m&&(l<m||!m)&&r.emit(W)}function f(m){var T=a.get().slice(),h=T.length;if(h){for(;T.length<m;)ht(T,T);ht(T.slice(-m),T.slice(0,m)).forEach(function(D,b){var x=b<m,S=v(D.slide,b);x?xt(S,T[0].slide):qe(u.list,S),ht(s,S),a.register(S,b-m+(x?0:h),D.index)})}}function v(m,T){var h=m.cloneNode(!0);return se(h,n.classes.clone),h.id=e.root.id+"-clone"+Bt(T+1),h}function _(){var m=n.clones;if(!e.is(Ve))m=0;else if(We(m)){var T=n[o("fixedWidth")]&&t.Layout.slideSize(0),h=T&&rt(ne(u.track)[o("width")]/T);m=h||n[o("autoWidth")]&&e.length||n.perPage*Hr}return m}return{mount:c,destroy:d}}function Wr(e,t,n){var r=Y(e),i=r.on,u=r.emit,a=e.state.set,o=t.Layout,s=o.slideSize,l=o.getPadding,c=o.totalSize,E=o.listSize,d=o.sliderSize,g=t.Direction,f=g.resolve,v=g.orient,_=t.Elements,m=_.list,T=_.track,h;function D(){h=t.Transition,i([Oe,zt,J,W],b)}function b(){t.Controller.isBusy()||(t.Scroll.cancel(),S(e.index),t.Slides.update())}function x(p,R,U,q){p!==R&&w(p>U)&&(y(),O(P(F(),p>U),!0)),a(Me),u(pe,R,U,p),h.start(R,function(){a(we),u(it,R,U,p),q&&q()})}function S(p){O(L(p,!0))}function O(p,R){if(!e.is(st)){var U=R?p:k(p);ie(m,"transform","translate"+f("X")+"("+U+"px)"),p!==U&&u(Rn)}}function k(p){if(e.is(Ve)){var R=I(p),U=R>t.Controller.getEnd(),q=R<0;(q||U)&&(p=P(p,U))}return p}function P(p,R){var U=p-$(R),q=d();return p-=v(q*(rt(Z(U)/q)||1))*(R?1:-1),p}function y(){O(F(),!0),h.cancel()}function I(p){for(var R=t.Slides.get(),U=0,q=1/0,X=0;X<R.length;X++){var Ie=R[X].index,A=Z(L(Ie,!0)-p);if(A<=q)q=A,U=Ie;else break}return U}function L(p,R){var U=v(c(p-1)-H(p));return R?G(U):U}function F(){var p=f("left");return ne(m)[p]-ne(T)[p]+v(l(!1))}function G(p){return n.trimSpace&&e.is(Ue)&&(p=Fe(p,0,v(d(!0)-E()))),p}function H(p){var R=n.focus;return R==="center"?(E()-s(p,!0))/2:+R*s(p)||0}function $(p){return L(p?t.Controller.getEnd():0,!!n.trimSpace)}function w(p){var R=v(P(F(),p));return p?R>=0:R<=m[f("scrollWidth")]-ne(T)[f("width")]}function C(p,R){R=We(R)?F():R;var U=p!==!0&&v(R)<v($(!1)),q=p!==!1&&v(R)>v($(!0));return U||q}return{mount:D,move:x,jump:S,translate:O,shift:P,cancel:y,toIndex:I,toPosition:L,getPosition:F,getLimit:$,exceededLimit:C,reposition:b}}function Xr(e,t,n){var r=Y(e),i=r.on,u=r.emit,a=t.Move,o=a.getPosition,s=a.getLimit,l=a.toPosition,c=t.Slides,E=c.isEnough,d=c.getLength,g=n.omitEnd,f=e.is(Ve),v=e.is(Ue),_=V(F,!1),m=V(F,!0),T=n.start||0,h,D=T,b,x,S;function O(){k(),i([J,W,At],k),i(zt,P)}function k(){b=d(!0),x=n.perMove,S=n.perPage,h=w();var A=Fe(T,0,g?h:b-1);A!==T&&(T=A,a.reposition())}function P(){h!==w()&&u(At)}function y(A,B,Q){if(!Ie()){var K=L(A),j=$(K);j>-1&&(B||j!==T)&&(U(j),a.move(K,j,D,Q))}}function I(A,B,Q,K){t.Scroll.scroll(A,B,Q,function(){var j=$(a.toIndex(o()));U(g?Se(j,h):j),K&&K()})}function L(A){var B=T;if(_e(A)){var Q=A.match(/([+\-<>])(\d+)?/)||[],K=Q[1],j=Q[2];K==="+"||K==="-"?B=G(T+ +(""+K+(+j||1)),T):K===">"?B=j?C(+j):_(!0):K==="<"&&(B=m(!0))}else B=f?A:Fe(A,0,h);return B}function F(A,B){var Q=x||(X()?1:S),K=G(T+Q*(A?-1:1),T,!(x||X()));return K===-1&&v&&!mn(o(),s(!A),1)?A?0:h:B?K:$(K)}function G(A,B,Q){if(E()||X()){var K=H(A);K!==A&&(B=A,A=K,Q=!1),A<0||A>h?!x&&(_t(0,A,B,!0)||_t(h,B,A,!0))?A=C(p(A)):f?A=Q?A<0?-(b%S||S):b:A:n.rewind?A=A<0?h:0:A=-1:Q&&A!==B&&(A=C(p(B)+(A<B?-1:1)))}else A=-1;return A}function H(A){if(v&&n.trimSpace==="move"&&A!==T)for(var B=o();B===l(A,!0)&&_t(A,0,e.length-1,!n.rewind);)A<T?--A:++A;return A}function $(A){return f?(A+b)%b||0:A}function w(){for(var A=b-(X()||f&&x?1:S);g&&A-- >0;)if(l(b-1,!0)!==l(A,!0)){A++;break}return Fe(A,0,b-1)}function C(A){return Fe(X()?A:S*A,0,h)}function p(A){return X()?Se(A,h):mt((A>=h?b-1:A)/S)}function R(A){var B=a.toIndex(A);return v?Fe(B,0,h):B}function U(A){A!==T&&(D=T,T=A)}function q(A){return A?D:T}function X(){return!We(n.focus)||n.isNavigation}function Ie(){return e.state.is([Me,He])&&!!n.waitForTransition}return{mount:O,go:y,scroll:I,getNext:_,getPrev:m,getAdjacent:F,getEnd:w,setIndex:U,getIndex:q,toIndex:C,toPage:p,toDest:R,hasFocus:X,isBusy:Ie}}var qr="http://www.w3.org/2000/svg",jr="m15.5 0.932-4.3 4.38 14.5 14.6-14.5 14.5 4.3 4.4 14.6-14.6 4.4-4.3-4.4-4.4-14.6-14.6z",Rt=40;function Zr(e,t,n){var r=Y(e),i=r.on,u=r.bind,a=r.emit,o=n.classes,s=n.i18n,l=t.Elements,c=t.Controller,E=l.arrows,d=l.track,g=E,f=l.prev,v=l.next,_,m,T={};function h(){b(),i(J,D)}function D(){x(),h()}function b(){var I=n.arrows;I&&!(f&&v)&&k(),f&&v&&(Qe(T,{prev:f,next:v}),et(g,I?"":"none"),se(g,m=qt+"--"+n.direction),I&&(S(),y(),M([f,v],ot,d.id),a(_r,f,v)))}function x(){r.destroy(),Ee(g,m),_?(Ne(E?[f,v]:g),f=v=null):ce([f,v],Wt)}function S(){i([Oe,it,W,Ge,At],y),u(v,"click",V(O,">")),u(f,"click",V(O,"<"))}function O(I){c.go(I,!0)}function k(){g=E||xe("div",o.arrows),f=P(!0),v=P(!1),_=!0,qe(g,[f,v]),!E&&xt(g,d)}function P(I){var L='<button class="'+o.arrow+" "+(I?o.prev:o.next)+'" type="button"><svg xmlns="'+qr+'" viewBox="0 0 '+Rt+" "+Rt+'" width="'+Rt+'" height="'+Rt+'" focusable="false"><path d="'+(n.arrowPath||jr)+'" />';return hn(L)}function y(){if(f&&v){var I=e.index,L=c.getPrev(),F=c.getNext(),G=L>-1&&I<L?s.last:s.prev,H=F>-1&&I>F?s.first:s.next;f.disabled=L<0,v.disabled=F<0,M(f,re,G),M(v,re,H),a(Ar,f,v,L,F)}}return{arrows:T,mount:h,destroy:x,update:y}}var Jr=kt+"-interval";function Qr(e,t,n){var r=Y(e),i=r.on,u=r.bind,a=r.emit,o=St(n.interval,e.go.bind(e,">"),S),s=o.isPaused,l=t.Elements,c=t.Elements,E=c.root,d=c.toggle,g=n.autoplay,f,v,_=g==="pause";function m(){g&&(T(),d&&M(d,ot,l.track.id),_||h(),x())}function T(){n.pauseOnHover&&u(E,"mouseenter mouseleave",function(k){f=k.type==="mouseenter",b()}),n.pauseOnFocus&&u(E,"focusin focusout",function(k){v=k.type==="focusin",b()}),d&&u(d,"click",function(){_?h():D(!0)}),i([pe,Yt,W],o.rewind),i(pe,O)}function h(){s()&&t.Slides.isEnough()&&(o.start(!n.resetProgress),v=f=_=!1,x(),a(Ln))}function D(k){k===void 0&&(k=!0),_=!!k,x(),s()||(o.pause(),a(Nn))}function b(){_||(f||v?D(!1):h())}function x(){d&&(de(d,Re,!_),M(d,re,n.i18n[_?"play":"pause"]))}function S(k){var P=l.bar;P&&ie(P,"width",k*100+"%"),a(yr,k)}function O(k){var P=t.Slides.getAt(k);o.set(P&&+ae(P.slide,Jr)||n.interval)}return{mount:m,destroy:o.cancel,play:h,pause:D,isPaused:s}}function ei(e,t,n){var r=Y(e),i=r.on;function u(){n.cover&&(i(bn,V(o,!0)),i([Oe,J,W],V(a,!0)))}function a(s){t.Slides.forEach(function(l){var c=Ze(l.container||l.slide,"img");c&&c.src&&o(s,c,l)})}function o(s,l,c){c.style("background",s?'center/cover no-repeat url("'+l.src+'")':"",!0),et(l,s?"none":"")}return{mount:u,destroy:V(a,!1)}}var ti=10,ni=600,ri=.6,ii=1.5,ai=800;function oi(e,t,n){var r=Y(e),i=r.on,u=r.emit,a=e.state.set,o=t.Move,s=o.getPosition,l=o.getLimit,c=o.exceededLimit,E=o.translate,d=e.is(Ue),g,f,v=1;function _(){i(pe,D),i([J,W],b)}function m(S,O,k,P,y){var I=s();if(D(),k&&(!d||!c())){var L=t.Layout.sliderSize(),F=Ut(S)*L*mt(Z(S)/L)||0;S=o.toPosition(t.Controller.toDest(S%L))+F}var G=mn(I,S,1);v=1,O=G?0:O||Tt(Z(S-I)/ii,ai),f=P,g=St(O,T,V(h,I,S,y),1),a(He),u(Yt),g.start()}function T(){a(we),f&&f(),u(Ge)}function h(S,O,k,P){var y=s(),I=S+(O-S)*x(P),L=(I-y)*v;E(y+L),d&&!k&&c()&&(v*=ri,Z(L)<ti&&m(l(c(!0)),ni,!1,f,!0))}function D(){g&&g.cancel()}function b(){g&&!g.isPaused()&&(D(),T())}function x(S){var O=n.easingFunc;return O?O(S):1-Math.pow(1-S,4)}return{mount:_,destroy:D,scroll:m,cancel:b}}var Be={passive:!1,capture:!0};function ui(e,t,n){var r=Y(e),i=r.on,u=r.emit,a=r.bind,o=r.unbind,s=e.state,l=t.Move,c=t.Scroll,E=t.Controller,d=t.Elements.track,g=t.Media.reduce,f=t.Direction,v=f.resolve,_=f.orient,m=l.getPosition,T=l.exceededLimit,h,D,b,x,S,O=!1,k,P,y;function I(){a(d,Jt,Pt,Be),a(d,Qt,Pt,Be),a(d,qn,F,Be),a(d,"click",$,{capture:!0}),a(d,"dragstart",ge),i([Oe,J],L)}function L(){var N=n.drag;ar(!N),x=N==="free"}function F(N){if(k=!1,!P){var z=j(N);K(N.target)&&(z||!N.button)&&(E.isBusy()?ge(N,!0):(y=z?d:window,S=s.is([Me,He]),b=null,a(y,Jt,G,Be),a(y,Qt,H,Be),l.cancel(),c.cancel(),w(N)))}}function G(N){if(s.is(dt)||(s.set(dt),u(Er)),N.cancelable)if(S){l.translate(h+Q(X(N)));var z=Ie(N)>Xn,Pe=O!==(O=T());(z||Pe)&&w(N),k=!0,u(hr),ge(N)}else R(N)&&(S=p(N),ge(N))}function H(N){s.is(dt)&&(s.set(we),u(Tr)),S&&(C(N),ge(N)),o(y,Jt,G),o(y,Qt,H),S=!1}function $(N){!P&&k&&ge(N,!0)}function w(N){b=D,D=N,h=m()}function C(N){var z=U(N),Pe=q(z),vt=n.rewind&&n.rewindByDrag;g(!1),x?E.scroll(Pe,0,n.snap):e.is(st)?E.go(_(Ut(z))<0?vt?"<":"-":vt?">":"+"):e.is(Ue)&&O&&vt?E.go(T(!0)?">":"<"):E.go(E.toDest(Pe),!0),g(!0)}function p(N){var z=n.dragMinThreshold,Pe=Ke(z),vt=Pe&&z.mouse||0,Pi=(Pe?z.touch:+z)||10;return Z(X(N))>(j(N)?Pi:vt)}function R(N){return Z(X(N))>Z(X(N,!0))}function U(N){if(e.is(Ve)||!O){var z=Ie(N);if(z&&z<Xn)return X(N)/z}return 0}function q(N){return m()+Ut(N)*Se(Z(N)*(n.flickPower||600),x?1/0:t.Layout.listSize()*(n.flickMaxPages||1))}function X(N,z){return B(N,z)-B(A(N),z)}function Ie(N){return Gt(N)-Gt(A(N))}function A(N){return D===N&&b||D}function B(N,z){return(j(N)?N.changedTouches[0]:N)["page"+v(z?"Y":"X")]}function Q(N){return N/(O&&e.is(Ue)?Vr:1)}function K(N){var z=n.noDrag;return!je(N,"."+Yn+", ."+bt)&&(!z||!je(N,z))}function j(N){return typeof TouchEvent<"u"&&N instanceof TouchEvent}function Ci(){return S}function ar(N){P=N}return{mount:I,disable:ar,isDragging:Ci}}var si={Spacebar:" ",Right:It,Left:yt,Up:Cn,Down:Pn};function en(e){return e=_e(e)?e:e.key,si[e]||e}var jn="keydown";function ci(e,t,n){var r=Y(e),i=r.on,u=r.bind,a=r.unbind,o=e.root,s=t.Direction.resolve,l,c;function E(){d(),i(J,g),i(J,d),i(pe,v)}function d(){var m=n.keyboard;m&&(l=m==="global"?window:o,u(l,jn,_))}function g(){a(l,jn)}function f(m){c=m}function v(){var m=c;c=!0,sn(function(){c=m})}function _(m){if(!c){var T=en(m);T===s(yt)?e.go("<"):T===s(It)&&e.go(">")}}return{mount:E,destroy:g,disable:f}}var ct=kt+"-lazy",Ct=ct+"-srcset",li="["+ct+"], ["+Ct+"]";function fi(e,t,n){var r=Y(e),i=r.on,u=r.off,a=r.bind,o=r.emit,s=n.lazyLoad==="sequential",l=[it,Ge],c=[];function E(){n.lazyLoad&&(d(),i(W,d))}function d(){ve(c),g(),s?m():(u(l),i(l,f),f())}function g(){t.Slides.forEach(function(T){Ft(T.slide,li).forEach(function(h){var D=ae(h,ct),b=ae(h,Ct);if(D!==h.src||b!==h.srcset){var x=n.classes.spinner,S=h.parentElement,O=Ze(S,"."+x)||xe("span",x,S);c.push([h,T,O]),h.src||et(h,"none")}})})}function f(){c=c.filter(function(T){var h=n.perPage*((n.preloadPages||1)+1)-1;return T[1].isWithin(e.index,h)?v(T):!0}),c.length||u(l)}function v(T){var h=T[0];se(T[1].slide,Zt),a(h,"load error",V(_,T)),M(h,"src",ae(h,ct)),M(h,"srcset",ae(h,Ct)),ce(h,ct),ce(h,Ct)}function _(T,h){var D=T[0],b=T[1];Ee(b.slide,Zt),h.type!=="error"&&(Ne(T[2]),et(D,""),o(bn,D,b),o(at)),s&&m()}function m(){c.length&&v(c.shift())}return{mount:E,destroy:V(ve,c),check:f}}function vi(e,t,n){var r=Y(e),i=r.on,u=r.emit,a=r.bind,o=t.Slides,s=t.Elements,l=t.Controller,c=l.hasFocus,E=l.getIndex,d=l.go,g=t.Direction.resolve,f=s.pagination,v=[],_,m;function T(){h(),i([J,W,At],T);var P=n.pagination;f&&et(f,P?"":"none"),P&&(i([pe,Yt,Ge],k),D(),k(),u(Sr,{list:_,items:v},O(e.index)))}function h(){_&&(Ne(f?me(_.children):_),Ee(_,m),ve(v),_=null),r.destroy()}function D(){var P=e.length,y=n.classes,I=n.i18n,L=n.perPage,F=c()?l.getEnd()+1:rt(P/L);_=f||xe("ul",y.pagination,s.track.parentElement),se(_,m=Ot+"--"+S()),M(_,he,"tablist"),M(_,re,I.select),M(_,Kt,S()===Lt?"vertical":"");for(var G=0;G<F;G++){var H=xe("li",null,_),$=xe("button",{class:y.page,type:"button"},H),w=o.getIn(G).map(function(p){return p.slide.id}),C=!c()&&L>1?I.pageX:I.slideX;a($,"click",V(b,G)),n.paginationKeyboard&&a($,"keydown",V(x,G)),M(H,he,"presentation"),M($,he,"tab"),M($,ot,w.join(" ")),M($,re,Vt(C,G+1)),M($,ke,-1),v.push({li:H,button:$,page:G})}}function b(P){d(">"+P,!0)}function x(P,y){var I=v.length,L=en(y),F=S(),G=-1;L===g(It,!1,F)?G=++P%I:L===g(yt,!1,F)?G=(--P+I)%I:L==="Home"?G=0:L==="End"&&(G=I-1);var H=v[G];H&&(gn(H.button),d(">"+G),ge(y,!0))}function S(){return n.paginationDirection||n.direction}function O(P){return v[l.toPage(P)]}function k(){var P=O(E(!0)),y=O(E());if(P){var I=P.button;Ee(I,Re),ce(I,Mn),M(I,ke,-1)}if(y){var L=y.button;se(L,Re),M(L,Mn,!0),M(L,ke,"")}u(pr,{list:_,items:v},P,y)}return{items:v,mount:T,destroy:h,getAt:O,update:k}}var di=[" ","Enter"];function gi(e,t,n){var r=n.isNavigation,i=n.slideFocus,u=[];function a(){e.splides.forEach(function(f){f.isParent||(l(e,f.splide),l(f.splide,e))}),r&&c()}function o(){u.forEach(function(f){f.destroy()}),ve(u)}function s(){o(),a()}function l(f,v){var _=Y(f);_.on(pe,function(m,T,h){v.go(v.is(Ve)?h:m)}),u.push(_)}function c(){var f=Y(e),v=f.on;v(pn,d),v(On,g),v([Oe,J],E),u.push(f),f.emit(In,e.splides)}function E(){M(t.Elements.list,Kt,n.direction===Lt?"vertical":"")}function d(f){e.go(f.index)}function g(f,v){Mt(di,en(v))&&(d(f),ge(v))}return{setup:V(t.Media.set,{slideFocus:We(i)?r:i},!0),mount:a,destroy:o,remount:s}}function Ei(e,t,n){var r=Y(e),i=r.bind,u=0;function a(){n.wheel&&i(t.Elements.track,"wheel",o,Be)}function o(l){if(l.cancelable){var c=l.deltaY,E=c<0,d=Gt(l),g=n.wheelMinThreshold||0,f=n.wheelSleep||0;Z(c)>g&&d-u>f&&(e.go(E?"<":">"),u=d),s(E)&&ge(l)}}function s(l){return!n.releaseWheel||e.state.is(Me)||t.Controller.getAdjacent(l)!==-1}return{mount:a}}var hi=90;function Ti(e,t,n){var r=Y(e),i=r.on,u=t.Elements.track,a=n.live&&!n.isNavigation,o=xe("span",xr),s=St(hi,V(c,!1));function l(){a&&(d(!t.Autoplay.isPaused()),M(u,kn,!0),o.textContent="…",i(Ln,V(d,!0)),i(Nn,V(d,!1)),i([it,Ge],V(c,!0)))}function c(g){M(u,Gn,g),g?(qe(u,o),s.start()):(Ne(o),s.cancel())}function E(){ce(u,[Fn,kn,Gn]),Ne(o)}function d(g){a&&M(u,Fn,g?"off":"polite")}return{mount:l,disable:d,destroy:E}}var mi=Object.freeze({__proto__:null,Media:Nr,Direction:br,Elements:Br,Slides:Yr,Layout:$r,Clones:Kr,Move:Wr,Controller:Xr,Arrows:Zr,Autoplay:Qr,Cover:ei,Scroll:oi,Drag:ui,Keyboard:ci,LazyLoad:fi,Pagination:vi,Sync:gi,Wheel:Ei,Live:Ti}),_i={prev:"Previous slide",next:"Next slide",first:"Go to first slide",last:"Go to last slide",slideX:"Go to slide %s",pageX:"Go to page %s",play:"Start autoplay",pause:"Pause autoplay",carousel:"carousel",slide:"slide",select:"Select a slide to show",slideLabel:"%s of %s"},Ai={type:"slide",role:"region",speed:400,perPage:1,cloneStatus:!0,arrows:!0,pagination:!0,paginationKeyboard:!0,interval:5e3,pauseOnHover:!0,pauseOnFocus:!0,resetProgress:!0,easing:"cubic-bezier(0.25, 1, 0.5, 1)",drag:!0,direction:"ltr",trimSpace:!0,focusableNodes:"a, button, textarea, input, select, iframe",live:!0,classes:kr,i18n:_i,reducedMotion:{speed:0,rewindSpeed:0,autoplay:"pause"}};function Si(e,t,n){var r=t.Slides;function i(){Y(e).on([Oe,W],u)}function u(){r.forEach(function(o){o.style("transform","translateX(-"+100*o.index+"%)")})}function a(o,s){r.style("transition","opacity "+n.speed+"ms "+n.easing),sn(s)}return{mount:i,start:a,cancel:Pt}}function pi(e,t,n){var r=t.Move,i=t.Controller,u=t.Scroll,a=t.Elements.list,o=V(ie,a,"transition"),s;function l(){Y(e).bind(a,"transitionend",function(g){g.target===a&&s&&(E(),s())})}function c(g,f){var v=r.toPosition(g,!0),_=r.getPosition(),m=d(g);Z(v-_)>=1&&m>=1?n.useScroll?u.scroll(v,m,!1,f):(o("transform "+m+"ms "+n.easing),r.translate(v,!0),s=f):(r.jump(g),f())}function E(){o(""),u.cancel()}function d(g){var f=n.rewindSpeed;if(e.is(Ue)&&f){var v=i.getIndex(!0),_=i.getEnd();if(v===0&&g>=_||v>=_&&g===0)return f}return n.speed}return{mount:l,start:c,cancel:E}}var yi=(function(){function e(n,r){this.event=Y(),this.Components={},this.state=Ir(De),this.splides=[],this._o={},this._E={};var i=_e(n)?Tn(document,n):n;nt(i,i+" is invalid."),this.root=i,r=Ae({label:ae(i,re)||"",labelledby:ae(i,Ht)||""},Ai,e.defaults,r||{});try{Ae(r,JSON.parse(ae(i,kt)))}catch{nt(!1,"Invalid JSON")}this._o=Object.create(Ae({},r))}var t=e.prototype;return t.mount=function(r,i){var u=this,a=this.state,o=this.Components;nt(a.is([De,gt]),"Already mounted!"),a.set(De),this._C=o,this._T=i||this._T||(this.is(st)?Si:pi),this._E=r||this._E;var s=Qe({},mi,this._E,{Transition:this._T});return Le(s,function(l,c){var E=l(u,o,u._o);o[c]=E,E.setup&&E.setup()}),Le(o,function(l){l.mount&&l.mount()}),this.emit(Oe),se(this.root,Fr),a.set(we),this.emit(Sn),this},t.sync=function(r){return this.splides.push({splide:r}),r.splides.push({splide:this,isParent:!0}),this.state.is(we)&&(this._C.Sync.remount(),r.Components.Sync.remount()),this},t.go=function(r){return this._C.Controller.go(r),this},t.on=function(r,i){return this.event.on(r,i),this},t.off=function(r){return this.event.off(r),this},t.emit=function(r){var i;return(i=this.event).emit.apply(i,[r].concat(me(arguments,1))),this},t.add=function(r,i){return this._C.Slides.add(r,i),this},t.remove=function(r){return this._C.Slides.remove(r),this},t.is=function(r){return this._o.type===r},t.refresh=function(){return this.emit(W),this},t.destroy=function(r){r===void 0&&(r=!0);var i=this.event,u=this.state;return u.is(De)?Y(this).on(Sn,this.destroy.bind(this,r)):(Le(this._C,function(a){a.destroy&&a.destroy(r)},!0),i.emit(yn),i.destroy(),r&&ve(this.splides),u.set(gt)),this},ur(e,[{key:"options",get:function(){return this._o},set:function(r){this._C.Media.set(r,!0,!0)}},{key:"length",get:function(){return this._C.Slides.getLength(!0)}},{key:"index",get:function(){return this._C.Controller.getIndex()}}]),e})(),tn=yi;tn.defaults={},tn.STATES=cr;const nn=e=>{try{return document.querySelector(e)}catch{return}},Zn=e=>{try{return document.querySelectorAll(e)}catch{return document.querySelectorAll("")}},Jn=e=>{try{return document.getElementById(e)}catch{return}},Ce=(e,t={},n="")=>{try{const r=document.createElement(e);for(const[i,u]of Object.entries(t))i==="className"?r.className=String(u):i==="id"?r.id=String(u):r.setAttribute(i,String(u));return n&&(r.innerHTML=n),r}catch{return document.createElement("div")}},lt=(e,t)=>{try{e.appendChild(t)}catch{}},Qn=(e,t)=>{try{e.prepend(t)}catch{}},Ii=e=>{try{e.remove()}catch{}},er=(e,t)=>{try{for(const[n,r]of Object.entries(t))e.style.setProperty(n,String(r))}catch{}},tr={USER_AGENT_SUBSTR_START:0,USER_AGENT_SUBSTR_LENGTH:4},ft={MOBILE:{GAP:"80px",PER_PAGE:4},DESKTOP:{GAP:"10px",PER_PAGE:7},AUTOPLAY_INTERVAL:3e3},ze={MIN_SLIDES_FOR_LOOP:2,MIN_SLIDES_FOR_AUTOPLAY:2,AUTOPLAY_INTERVAL:3e3,TRANSITION_SPEED:800,GAP:"10px",SPLIDE_INIT_DELAY:100},Li={MAX_ERROR_LOG_ENTRIES:50,DOM_READY_TIMEOUT:5e3},ee={EMPTY_LENGTH:0,FIRST_INDEX:0,NOT_FOUND_INDEX:-1,NEXT_ITEM_OFFSET:1,LAST_ITEM_OFFSET:-1},nr={CHECK_INTERVAL:10,DATA_CHECK_INTERVAL:100},rr={SPLIDE_TAG_CONTAINER_ID:"splideTagContainer",SPLIDE_TAG_WRAPPER_ID:"splideTagWrapper"},ir={ID:"wusong8899-tag-tiles",TRANSLATION_PREFIX:"wusong8899-tag-tiles"},rn=()=>{try{const{userAgent:e}=navigator;return e.substring(tr.USER_AGENT_SUBSTR_START,tr.USER_AGENT_SUBSTR_LENGTH)==="Mobi"}catch{return!1}},ue={env:"production",app:{extensionId:ir.ID,translationPrefix:ir.TRANSLATION_PREFIX},tagTiles:{autoplayInterval:ft.AUTOPLAY_INTERVAL,checkInterval:nr.CHECK_INTERVAL,dataCheckInterval:nr.DATA_CHECK_INTERVAL,mobile:{gap:ft.MOBILE.GAP,perPage:ft.MOBILE.PER_PAGE},desktop:{gap:ft.DESKTOP.GAP,perPage:ft.DESKTOP.PER_PAGE},advanced:{minSlidesForLoop:ze.MIN_SLIDES_FOR_LOOP,enableAutoplay:!0,autoplayInterval:ze.AUTOPLAY_INTERVAL,enableLoopMode:!0,transitionSpeed:ze.TRANSITION_SPEED,gap:ze.GAP,pauseOnMouseEnter:!0,enableGrabCursor:!0,enableFreeMode:!1}},ui:{tagContainerId:rr.SPLIDE_TAG_CONTAINER_ID,tagWrapperId:rr.SPLIDE_TAG_WRAPPER_ID}},Ni="wusong8899-tag-tiles",bi=e=>{try{const t=fe&&fe.forum,n=t&&t.attribute;return typeof n=="function"?n.call(t,e):void 0}catch{return}},Te=(e,t)=>{const n=bi(`${Ni}.${e}`),r=1;if(typeof n<"u"&&n!==null){if(typeof t=="boolean")return n===!0||n==="1"||n===r;if(typeof t=="number"){const i=Number(n);return Number.isNaN(i)?t:i}return n}return t},Oi=()=>{const e=ue.tagTiles.advanced;return{minSlidesForLoop:Te("AdvancedSplideMinSlidesForLoop",e.minSlidesForLoop),enableAutoplay:Te("AdvancedSplideEnableAutoplay",e.enableAutoplay),autoplayInterval:Te("AdvancedSplideAutoplayInterval",e.autoplayInterval),enableLoopMode:Te("AdvancedSplideEnableLoopMode",e.enableLoopMode),transitionSpeed:Te("AdvancedSplideTransitionSpeed",e.transitionSpeed),gap:Te("AdvancedSplideGap",e.gap),pauseOnMouseEnter:Te("AdvancedSplidePauseOnMouseEnter",e.pauseOnMouseEnter),enableGrabCursor:Te("AdvancedSplideEnableGrabCursor",e.enableGrabCursor),enableFreeMode:Te("AdvancedSplideEnableFreeMode",e.enableFreeMode)}};class Ri{changeCategoryLayout(){try{if(Jn(ue.ui.tagContainerId))return;const t=Zn(".TagTile");t.length>ee.EMPTY_LENGTH?this.processTagTiles(t):this.waitForTagTilesAndProcess()}catch{}}waitForTagTilesAndProcess(){let r=0;const i=()=>{r+=ee.NEXT_ITEM_OFFSET;const u=Zn(".TagTile");u.length>ee.EMPTY_LENGTH?this.processTagTiles(u):r<10&&setTimeout(i,200)};i()}processTagTiles(t){try{const n=this.createTagSplideContainer();if(!n)return;const r=this.createTagSplide(n);if(!r)return;const i=this.createTagSplideWrapper(r);if(!i)return;this.populateTagSlides(i,t),this.appendTagContainer(n),this.addTagSplideContent(n),this.removeOriginalTagTiles(),this.setupMobileStyles(),this.initializeTagSplide(),this.notifyTagsLayoutChanged()}catch{}}createTagSplideContainer(){const t=Ce("div",{className:"splideTagContainer",id:ue.ui.tagContainerId}),n=Ce("div",{className:"TagTextOuterContainer"});return lt(t,n),t}createTagSplide(t){const n=Ce("div",{className:"splide tagSplide"});return lt(t,n),n}createTagSplideWrapper(t){const n=Ce("div",{className:"splide__track"});lt(t,n);const r=Ce("ul",{className:"splide__list",id:ue.ui.tagWrapperId});return lt(n,r),r}populateTagSlides(t,n){const r=rn();for(const i of n){const u=i,a=this.extractTagData(u);if(a){const o=this.createTagSlide(a,r);lt(t,o)}}}extractTagData(t){const n=t.querySelector("a"),r=t.querySelector(".TagTile-name"),i=t.querySelector(".TagTile-description");if(!n||!r)return;const u=this.getTagBackgroundImage(n.href,t),a=globalThis.getComputedStyle(t),o=u||a.background;let s="",l="";return i&&(s=i.textContent||"",l=globalThis.getComputedStyle(i).color),{url:n.href,background:o,name:r.textContent||"",nameColor:globalThis.getComputedStyle(r).color,description:s,descColor:l}}getTagBackgroundImage(t,n){try{const i=new URL(t,globalThis.location.origin).pathname.split("/").filter(Boolean),u=i.indexOf("t"),a=i.indexOf("tags");let o="";if(u!==ee.NOT_FOUND_INDEX&&i[u+ee.NEXT_ITEM_OFFSET]?o=i[u+ee.NEXT_ITEM_OFFSET]:a!==ee.NOT_FOUND_INDEX&&i[a+ee.NEXT_ITEM_OFFSET]?o=i[a+ee.NEXT_ITEM_OFFSET]:i.length>ee.EMPTY_LENGTH&&(o=i[i.length+ee.LAST_ITEM_OFFSET]),!o)return;const s=this.getTagBackgroundUrlBySlug(o);return s?`url(${s})`:void 0}catch{const r=n.style.background;return r&&r.includes("url(")?r:void 0}}getTagBackgroundUrlBySlug(t){try{const r=fe.store.all("tags").find(u=>{const a=u;let o="";return typeof a.slug=="function"?o=a.slug():a.attribute&&typeof a.attribute=="function"&&(o=a.attribute("slug")),o===t});if(!r)return;const i=r;if(i.attribute&&typeof i.attribute=="function"){const u=i.attribute("wusong8899BackgroundURL");if(u)return u}return}catch{return}}createTagSlide(t,n){const r=Ce("li",{className:"splide__slide splide__slide-tag"});let i="splide__slide-tag-inner";n&&(i="splide__slide-tag-inner-mobile");const u=`background:${t.background};background-size: cover;background-position: center;background-repeat: no-repeat;`,a=this.hasBackgroundImage(t.background);let o="";return a||(o=`
            <div style='font-weight:bold;font-size:14px;color:${t.nameColor}'>
                ${t.name}
            </div>
        `),r.innerHTML=`
            <a href='${t.url}'>
                <div class='${i}' style='${u}'>
                    ${o}
                </div>
            </a>
        `,r}hasBackgroundImage(t){return t?t.includes("url(")&&!t.includes("url()"):!1}appendTagContainer(t){const n=nn("#content .container .TagsPage-content");n&&Qn(n,t)}addTagSplideContent(t){const n=t.querySelector(".TagTextOuterContainer");if(n){const r=Ce("div",{className:"TagTextContainer"},"<div class='TagTextIcon'></div>中文玩家社区资讯");Qn(n,r);const i=this.createSocialButtonsHTML();n.insertAdjacentHTML("beforeend",i)}}createSocialButtonsHTML(){const{extensionId:t}=ue.app,r=[{urlKey:`${t}.SocialKickUrl`,iconKey:`${t}.SocialKickIcon`,defaultIcon:""},{urlKey:`${t}.SocialFacebookUrl`,iconKey:`${t}.SocialFacebookIcon`,defaultIcon:""},{urlKey:`${t}.SocialTwitterUrl`,iconKey:`${t}.SocialTwitterIcon`,defaultIcon:""},{urlKey:`${t}.SocialYouTubeUrl`,iconKey:`${t}.SocialYouTubeIcon`,defaultIcon:""},{urlKey:`${t}.SocialInstagramUrl`,iconKey:`${t}.SocialInstagramIcon`,defaultIcon:""}].map((i,u)=>{const a=fe.forum.attribute(i.urlKey)||"",o=fe.forum.attribute(i.iconKey)||i.defaultIcon;if(!a.trim()||!o.trim())return"";let s="";return u>ee.FIRST_INDEX&&(s="margin-left: 20px;"),`<img onClick="window.open('${a}', '_blank')" style="width: 32px;${s}" src="${o}">`}).filter(i=>i!=="").join("");return r?`
            <div style="text-align:center;padding-top: 10px;">
                <button class="Button Button--primary" type="button" style="font-weight: normal !important; color:#ffa000; background: #1a1d2e !important;border-radius: 2rem !important;">
                    <div style="margin-top: 5px;" class="Button-label">
                        ${r}
                    </div>
                </button>
            </div>
        `:""}removeOriginalTagTiles(){const t=nn(".TagTiles");t&&Ii(t)}setupMobileStyles(){if(rn()){const t=Jn("app"),n=nn(".App-content");t&&er(t,{"overflow-x":"hidden"}),n&&er(n,{"min-height":"auto",background:""})}}initializeTagSplide(){const t=Oi();setTimeout(()=>{try{const n=document.querySelectorAll(".tagSplide .splide__slide"),r=n.length>=t.minSlidesForLoop,i=t.enableLoopMode&&r;let u=!1;t.enableAutoplay&&n.length>=ze.MIN_SLIDES_FOR_AUTOPLAY&&(u={interval:t.autoplayInterval,pauseOnHover:t.pauseOnMouseEnter,pauseOnFocus:!0,rewind:!1});const o=rn(),s=3;let l="auto";o&&(l=s);let c="slide";i&&(c="loop");const E=new tn(".tagSplide",{perPage:l,gap:t.gap,type:c,autoplay:u,speed:t.transitionSpeed,drag:t.enableGrabCursor,focus:ee.EMPTY_LENGTH,pagination:!1,arrows:!0});E.mount()}catch{}},ze.SPLIDE_INIT_DELAY)}notifyTagsLayoutChanged(){try{const t=new CustomEvent("tagsLayoutChanged",{detail:{extensionId:ue.app.extensionId,layoutType:"splide"}});document.dispatchEvent(t)}catch{}}}class Ye{constructor(){this.errorLog=[],this.isInitialized=!1}static getInstance(){return Ye.instance||(Ye.instance=new Ye),Ye.instance}initialize(){try{return this.isInitialized||(this.setupGlobalErrorHandling(),this.isInitialized=!0),!0}catch{return!1}}handleSync(t,n){try{return t()}catch(r){this.logError(r,n);return}}handleAsync(t,n){return t().catch(r=>{this.logError(r,n)})}logError(t,n){try{const r={timestamp:new Date,error:t,context:n};this.errorLog.push(r),this.errorLog.length>Li.MAX_ERROR_LOG_ENTRIES&&this.errorLog.shift()}catch{}}setupGlobalErrorHandling(){try{globalThis.addEventListener("unhandledrejection",t=>{this.logError(new Error(String(t.reason)),"Unhandled Promise Rejection")})}catch{}}getErrorLog(){return[...this.errorLog]}clearErrorLog(){this.errorLog=[]}}class $e{constructor(){}static getInstance(){return $e.instance||($e.instance=new $e),$e.instance}isTagsPage(){try{return fe.current.get("routeName")==="tags"}catch{try{return globalThis.location.pathname.includes("/tags")}catch{return!1}}}getConfig(){return ue}isConfigured(){try{const t=["Kick","Facebook","Twitter","YouTube","Instagram"];for(const n of t){const r=fe.forum.attribute(`${ue.app.extensionId}.Social${n}Url`),i=fe.forum.attribute(`${ue.app.extensionId}.Social${n}Icon`);if(r&&i)return!0}return!1}catch{return!1}}}fe.initializers.add(ue.app.extensionId,()=>{const e=Ye.getInstance(),t=$e.getInstance();if(!e.initialize())return;const n=new Ri;an.extend(on.prototype,"oncreate",function(i){e.handleSync(()=>{t.isTagsPage()&&setTimeout(()=>{n.changeCategoryLayout()},100)},"TagsPage oncreate extension")}),an.extend(on.prototype,"onupdate",function(i){e.handleSync(()=>{document.getElementById(ue.ui.tagContainerId)||setTimeout(()=>{n.changeCategoryLayout()},100)},"TagsPage onupdate extension")})})})(flarum.core.compat["common/extend"],flarum.core.compat["forum/app"],flarum.core.compat["tags/components/TagsPage"]);
//# sourceMappingURL=forum.js.map

module.exports={};