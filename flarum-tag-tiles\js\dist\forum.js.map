{"version": 3, "file": "forum.js", "sources": ["../node_modules/.pnpm/@splidejs+splide@4.1.4/node_modules/@splidejs/splide/dist/js/splide.esm.js", "../src/forum/utils/dom-utils.ts", "../src/common/config/constants.ts", "../src/forum/utils/mobile-detection.ts", "../src/common/config/defaults.ts", "../src/forum/utils/config-reader.ts", "../src/forum/components/tag-tiles-manager.ts", "../src/forum/utils/error-handler.ts", "../src/forum/utils/config-manager.ts", "../src/forum/index.ts"], "sourcesContent": ["function _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }\n\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, \"prototype\", { writable: false }); return Constructor; }\n\n/*!\n * Splide.js\n * Version  : 4.1.4\n * License  : MIT\n * Copyright: 2022 Naotoshi Fujita\n */\nvar MEDIA_PREFERS_REDUCED_MOTION = \"(prefers-reduced-motion: reduce)\";\nvar CREATED = 1;\nvar MOUNTED = 2;\nvar IDLE = 3;\nvar MOVING = 4;\nvar SCROLLING = 5;\nvar DRAGGING = 6;\nvar DESTROYED = 7;\nvar STATES = {\n  CREATED: CREATED,\n  MOUNTED: MOUNTED,\n  IDLE: IDLE,\n  MOVING: MOVING,\n  SCROLLING: SCROLLING,\n  DRAGGING: DRAGGING,\n  DESTROYED: DESTROYED\n};\n\nfunction empty(array) {\n  array.length = 0;\n}\n\nfunction slice(arrayLike, start, end) {\n  return Array.prototype.slice.call(arrayLike, start, end);\n}\n\nfunction apply(func) {\n  return func.bind.apply(func, [null].concat(slice(arguments, 1)));\n}\n\nvar nextTick = setTimeout;\n\nvar noop = function noop() {};\n\nfunction raf(func) {\n  return requestAnimationFrame(func);\n}\n\nfunction typeOf(type, subject) {\n  return typeof subject === type;\n}\n\nfunction isObject(subject) {\n  return !isNull(subject) && typeOf(\"object\", subject);\n}\n\nvar isArray = Array.isArray;\nvar isFunction = apply(typeOf, \"function\");\nvar isString = apply(typeOf, \"string\");\nvar isUndefined = apply(typeOf, \"undefined\");\n\nfunction isNull(subject) {\n  return subject === null;\n}\n\nfunction isHTMLElement(subject) {\n  try {\n    return subject instanceof (subject.ownerDocument.defaultView || window).HTMLElement;\n  } catch (e) {\n    return false;\n  }\n}\n\nfunction toArray(value) {\n  return isArray(value) ? value : [value];\n}\n\nfunction forEach(values, iteratee) {\n  toArray(values).forEach(iteratee);\n}\n\nfunction includes(array, value) {\n  return array.indexOf(value) > -1;\n}\n\nfunction push(array, items) {\n  array.push.apply(array, toArray(items));\n  return array;\n}\n\nfunction toggleClass(elm, classes, add) {\n  if (elm) {\n    forEach(classes, function (name) {\n      if (name) {\n        elm.classList[add ? \"add\" : \"remove\"](name);\n      }\n    });\n  }\n}\n\nfunction addClass(elm, classes) {\n  toggleClass(elm, isString(classes) ? classes.split(\" \") : classes, true);\n}\n\nfunction append(parent, children) {\n  forEach(children, parent.appendChild.bind(parent));\n}\n\nfunction before(nodes, ref) {\n  forEach(nodes, function (node) {\n    var parent = (ref || node).parentNode;\n\n    if (parent) {\n      parent.insertBefore(node, ref);\n    }\n  });\n}\n\nfunction matches(elm, selector) {\n  return isHTMLElement(elm) && (elm[\"msMatchesSelector\"] || elm.matches).call(elm, selector);\n}\n\nfunction children(parent, selector) {\n  var children2 = parent ? slice(parent.children) : [];\n  return selector ? children2.filter(function (child) {\n    return matches(child, selector);\n  }) : children2;\n}\n\nfunction child(parent, selector) {\n  return selector ? children(parent, selector)[0] : parent.firstElementChild;\n}\n\nvar ownKeys = Object.keys;\n\nfunction forOwn(object, iteratee, right) {\n  if (object) {\n    (right ? ownKeys(object).reverse() : ownKeys(object)).forEach(function (key) {\n      key !== \"__proto__\" && iteratee(object[key], key);\n    });\n  }\n\n  return object;\n}\n\nfunction assign(object) {\n  slice(arguments, 1).forEach(function (source) {\n    forOwn(source, function (value, key) {\n      object[key] = source[key];\n    });\n  });\n  return object;\n}\n\nfunction merge(object) {\n  slice(arguments, 1).forEach(function (source) {\n    forOwn(source, function (value, key) {\n      if (isArray(value)) {\n        object[key] = value.slice();\n      } else if (isObject(value)) {\n        object[key] = merge({}, isObject(object[key]) ? object[key] : {}, value);\n      } else {\n        object[key] = value;\n      }\n    });\n  });\n  return object;\n}\n\nfunction omit(object, keys) {\n  forEach(keys || ownKeys(object), function (key) {\n    delete object[key];\n  });\n}\n\nfunction removeAttribute(elms, attrs) {\n  forEach(elms, function (elm) {\n    forEach(attrs, function (attr) {\n      elm && elm.removeAttribute(attr);\n    });\n  });\n}\n\nfunction setAttribute(elms, attrs, value) {\n  if (isObject(attrs)) {\n    forOwn(attrs, function (value2, name) {\n      setAttribute(elms, name, value2);\n    });\n  } else {\n    forEach(elms, function (elm) {\n      isNull(value) || value === \"\" ? removeAttribute(elm, attrs) : elm.setAttribute(attrs, String(value));\n    });\n  }\n}\n\nfunction create(tag, attrs, parent) {\n  var elm = document.createElement(tag);\n\n  if (attrs) {\n    isString(attrs) ? addClass(elm, attrs) : setAttribute(elm, attrs);\n  }\n\n  parent && append(parent, elm);\n  return elm;\n}\n\nfunction style(elm, prop, value) {\n  if (isUndefined(value)) {\n    return getComputedStyle(elm)[prop];\n  }\n\n  if (!isNull(value)) {\n    elm.style[prop] = \"\" + value;\n  }\n}\n\nfunction display(elm, display2) {\n  style(elm, \"display\", display2);\n}\n\nfunction focus(elm) {\n  elm[\"setActive\"] && elm[\"setActive\"]() || elm.focus({\n    preventScroll: true\n  });\n}\n\nfunction getAttribute(elm, attr) {\n  return elm.getAttribute(attr);\n}\n\nfunction hasClass(elm, className) {\n  return elm && elm.classList.contains(className);\n}\n\nfunction rect(target) {\n  return target.getBoundingClientRect();\n}\n\nfunction remove(nodes) {\n  forEach(nodes, function (node) {\n    if (node && node.parentNode) {\n      node.parentNode.removeChild(node);\n    }\n  });\n}\n\nfunction parseHtml(html) {\n  return child(new DOMParser().parseFromString(html, \"text/html\").body);\n}\n\nfunction prevent(e, stopPropagation) {\n  e.preventDefault();\n\n  if (stopPropagation) {\n    e.stopPropagation();\n    e.stopImmediatePropagation();\n  }\n}\n\nfunction query(parent, selector) {\n  return parent && parent.querySelector(selector);\n}\n\nfunction queryAll(parent, selector) {\n  return selector ? slice(parent.querySelectorAll(selector)) : [];\n}\n\nfunction removeClass(elm, classes) {\n  toggleClass(elm, classes, false);\n}\n\nfunction timeOf(e) {\n  return e.timeStamp;\n}\n\nfunction unit(value) {\n  return isString(value) ? value : value ? value + \"px\" : \"\";\n}\n\nvar PROJECT_CODE = \"splide\";\nvar DATA_ATTRIBUTE = \"data-\" + PROJECT_CODE;\n\nfunction assert(condition, message) {\n  if (!condition) {\n    throw new Error(\"[\" + PROJECT_CODE + \"] \" + (message || \"\"));\n  }\n}\n\nvar min = Math.min,\n    max = Math.max,\n    floor = Math.floor,\n    ceil = Math.ceil,\n    abs = Math.abs;\n\nfunction approximatelyEqual(x, y, epsilon) {\n  return abs(x - y) < epsilon;\n}\n\nfunction between(number, x, y, exclusive) {\n  var minimum = min(x, y);\n  var maximum = max(x, y);\n  return exclusive ? minimum < number && number < maximum : minimum <= number && number <= maximum;\n}\n\nfunction clamp(number, x, y) {\n  var minimum = min(x, y);\n  var maximum = max(x, y);\n  return min(max(minimum, number), maximum);\n}\n\nfunction sign(x) {\n  return +(x > 0) - +(x < 0);\n}\n\nfunction camelToKebab(string) {\n  return string.replace(/([a-z0-9])([A-Z])/g, \"$1-$2\").toLowerCase();\n}\n\nfunction format(string, replacements) {\n  forEach(replacements, function (replacement) {\n    string = string.replace(\"%s\", \"\" + replacement);\n  });\n  return string;\n}\n\nfunction pad(number) {\n  return number < 10 ? \"0\" + number : \"\" + number;\n}\n\nvar ids = {};\n\nfunction uniqueId(prefix) {\n  return \"\" + prefix + pad(ids[prefix] = (ids[prefix] || 0) + 1);\n}\n\nfunction EventBinder() {\n  var listeners = [];\n\n  function bind(targets, events, callback, options) {\n    forEachEvent(targets, events, function (target, event, namespace) {\n      var isEventTarget = (\"addEventListener\" in target);\n      var remover = isEventTarget ? target.removeEventListener.bind(target, event, callback, options) : target[\"removeListener\"].bind(target, callback);\n      isEventTarget ? target.addEventListener(event, callback, options) : target[\"addListener\"](callback);\n      listeners.push([target, event, namespace, callback, remover]);\n    });\n  }\n\n  function unbind(targets, events, callback) {\n    forEachEvent(targets, events, function (target, event, namespace) {\n      listeners = listeners.filter(function (listener) {\n        if (listener[0] === target && listener[1] === event && listener[2] === namespace && (!callback || listener[3] === callback)) {\n          listener[4]();\n          return false;\n        }\n\n        return true;\n      });\n    });\n  }\n\n  function dispatch(target, type, detail) {\n    var e;\n    var bubbles = true;\n\n    if (typeof CustomEvent === \"function\") {\n      e = new CustomEvent(type, {\n        bubbles: bubbles,\n        detail: detail\n      });\n    } else {\n      e = document.createEvent(\"CustomEvent\");\n      e.initCustomEvent(type, bubbles, false, detail);\n    }\n\n    target.dispatchEvent(e);\n    return e;\n  }\n\n  function forEachEvent(targets, events, iteratee) {\n    forEach(targets, function (target) {\n      target && forEach(events, function (events2) {\n        events2.split(\" \").forEach(function (eventNS) {\n          var fragment = eventNS.split(\".\");\n          iteratee(target, fragment[0], fragment[1]);\n        });\n      });\n    });\n  }\n\n  function destroy() {\n    listeners.forEach(function (data) {\n      data[4]();\n    });\n    empty(listeners);\n  }\n\n  return {\n    bind: bind,\n    unbind: unbind,\n    dispatch: dispatch,\n    destroy: destroy\n  };\n}\n\nvar EVENT_MOUNTED = \"mounted\";\nvar EVENT_READY = \"ready\";\nvar EVENT_MOVE = \"move\";\nvar EVENT_MOVED = \"moved\";\nvar EVENT_CLICK = \"click\";\nvar EVENT_ACTIVE = \"active\";\nvar EVENT_INACTIVE = \"inactive\";\nvar EVENT_VISIBLE = \"visible\";\nvar EVENT_HIDDEN = \"hidden\";\nvar EVENT_REFRESH = \"refresh\";\nvar EVENT_UPDATED = \"updated\";\nvar EVENT_RESIZE = \"resize\";\nvar EVENT_RESIZED = \"resized\";\nvar EVENT_DRAG = \"drag\";\nvar EVENT_DRAGGING = \"dragging\";\nvar EVENT_DRAGGED = \"dragged\";\nvar EVENT_SCROLL = \"scroll\";\nvar EVENT_SCROLLED = \"scrolled\";\nvar EVENT_OVERFLOW = \"overflow\";\nvar EVENT_DESTROY = \"destroy\";\nvar EVENT_ARROWS_MOUNTED = \"arrows:mounted\";\nvar EVENT_ARROWS_UPDATED = \"arrows:updated\";\nvar EVENT_PAGINATION_MOUNTED = \"pagination:mounted\";\nvar EVENT_PAGINATION_UPDATED = \"pagination:updated\";\nvar EVENT_NAVIGATION_MOUNTED = \"navigation:mounted\";\nvar EVENT_AUTOPLAY_PLAY = \"autoplay:play\";\nvar EVENT_AUTOPLAY_PLAYING = \"autoplay:playing\";\nvar EVENT_AUTOPLAY_PAUSE = \"autoplay:pause\";\nvar EVENT_LAZYLOAD_LOADED = \"lazyload:loaded\";\nvar EVENT_SLIDE_KEYDOWN = \"sk\";\nvar EVENT_SHIFTED = \"sh\";\nvar EVENT_END_INDEX_CHANGED = \"ei\";\n\nfunction EventInterface(Splide2) {\n  var bus = Splide2 ? Splide2.event.bus : document.createDocumentFragment();\n  var binder = EventBinder();\n\n  function on(events, callback) {\n    binder.bind(bus, toArray(events).join(\" \"), function (e) {\n      callback.apply(callback, isArray(e.detail) ? e.detail : []);\n    });\n  }\n\n  function emit(event) {\n    binder.dispatch(bus, event, slice(arguments, 1));\n  }\n\n  if (Splide2) {\n    Splide2.event.on(EVENT_DESTROY, binder.destroy);\n  }\n\n  return assign(binder, {\n    bus: bus,\n    on: on,\n    off: apply(binder.unbind, bus),\n    emit: emit\n  });\n}\n\nfunction RequestInterval(interval, onInterval, onUpdate, limit) {\n  var now = Date.now;\n  var startTime;\n  var rate = 0;\n  var id;\n  var paused = true;\n  var count = 0;\n\n  function update() {\n    if (!paused) {\n      rate = interval ? min((now() - startTime) / interval, 1) : 1;\n      onUpdate && onUpdate(rate);\n\n      if (rate >= 1) {\n        onInterval();\n        startTime = now();\n\n        if (limit && ++count >= limit) {\n          return pause();\n        }\n      }\n\n      id = raf(update);\n    }\n  }\n\n  function start(resume) {\n    resume || cancel();\n    startTime = now() - (resume ? rate * interval : 0);\n    paused = false;\n    id = raf(update);\n  }\n\n  function pause() {\n    paused = true;\n  }\n\n  function rewind() {\n    startTime = now();\n    rate = 0;\n\n    if (onUpdate) {\n      onUpdate(rate);\n    }\n  }\n\n  function cancel() {\n    id && cancelAnimationFrame(id);\n    rate = 0;\n    id = 0;\n    paused = true;\n  }\n\n  function set(time) {\n    interval = time;\n  }\n\n  function isPaused() {\n    return paused;\n  }\n\n  return {\n    start: start,\n    rewind: rewind,\n    pause: pause,\n    cancel: cancel,\n    set: set,\n    isPaused: isPaused\n  };\n}\n\nfunction State(initialState) {\n  var state = initialState;\n\n  function set(value) {\n    state = value;\n  }\n\n  function is(states) {\n    return includes(toArray(states), state);\n  }\n\n  return {\n    set: set,\n    is: is\n  };\n}\n\nfunction Throttle(func, duration) {\n  var interval = RequestInterval(duration || 0, func, null, 1);\n  return function () {\n    interval.isPaused() && interval.start();\n  };\n}\n\nfunction Media(Splide2, Components2, options) {\n  var state = Splide2.state;\n  var breakpoints = options.breakpoints || {};\n  var reducedMotion = options.reducedMotion || {};\n  var binder = EventBinder();\n  var queries = [];\n\n  function setup() {\n    var isMin = options.mediaQuery === \"min\";\n    ownKeys(breakpoints).sort(function (n, m) {\n      return isMin ? +n - +m : +m - +n;\n    }).forEach(function (key) {\n      register(breakpoints[key], \"(\" + (isMin ? \"min\" : \"max\") + \"-width:\" + key + \"px)\");\n    });\n    register(reducedMotion, MEDIA_PREFERS_REDUCED_MOTION);\n    update();\n  }\n\n  function destroy(completely) {\n    if (completely) {\n      binder.destroy();\n    }\n  }\n\n  function register(options2, query) {\n    var queryList = matchMedia(query);\n    binder.bind(queryList, \"change\", update);\n    queries.push([options2, queryList]);\n  }\n\n  function update() {\n    var destroyed = state.is(DESTROYED);\n    var direction = options.direction;\n    var merged = queries.reduce(function (merged2, entry) {\n      return merge(merged2, entry[1].matches ? entry[0] : {});\n    }, {});\n    omit(options);\n    set(merged);\n\n    if (options.destroy) {\n      Splide2.destroy(options.destroy === \"completely\");\n    } else if (destroyed) {\n      destroy(true);\n      Splide2.mount();\n    } else {\n      direction !== options.direction && Splide2.refresh();\n    }\n  }\n\n  function reduce(enable) {\n    if (matchMedia(MEDIA_PREFERS_REDUCED_MOTION).matches) {\n      enable ? merge(options, reducedMotion) : omit(options, ownKeys(reducedMotion));\n    }\n  }\n\n  function set(opts, base, notify) {\n    merge(options, opts);\n    base && merge(Object.getPrototypeOf(options), opts);\n\n    if (notify || !state.is(CREATED)) {\n      Splide2.emit(EVENT_UPDATED, options);\n    }\n  }\n\n  return {\n    setup: setup,\n    destroy: destroy,\n    reduce: reduce,\n    set: set\n  };\n}\n\nvar ARROW = \"Arrow\";\nvar ARROW_LEFT = ARROW + \"Left\";\nvar ARROW_RIGHT = ARROW + \"Right\";\nvar ARROW_UP = ARROW + \"Up\";\nvar ARROW_DOWN = ARROW + \"Down\";\nvar LTR = \"ltr\";\nvar RTL = \"rtl\";\nvar TTB = \"ttb\";\nvar ORIENTATION_MAP = {\n  width: [\"height\"],\n  left: [\"top\", \"right\"],\n  right: [\"bottom\", \"left\"],\n  x: [\"y\"],\n  X: [\"Y\"],\n  Y: [\"X\"],\n  ArrowLeft: [ARROW_UP, ARROW_RIGHT],\n  ArrowRight: [ARROW_DOWN, ARROW_LEFT]\n};\n\nfunction Direction(Splide2, Components2, options) {\n  function resolve(prop, axisOnly, direction) {\n    direction = direction || options.direction;\n    var index = direction === RTL && !axisOnly ? 1 : direction === TTB ? 0 : -1;\n    return ORIENTATION_MAP[prop] && ORIENTATION_MAP[prop][index] || prop.replace(/width|left|right/i, function (match, offset) {\n      var replacement = ORIENTATION_MAP[match.toLowerCase()][index] || match;\n      return offset > 0 ? replacement.charAt(0).toUpperCase() + replacement.slice(1) : replacement;\n    });\n  }\n\n  function orient(value) {\n    return value * (options.direction === RTL ? 1 : -1);\n  }\n\n  return {\n    resolve: resolve,\n    orient: orient\n  };\n}\n\nvar ROLE = \"role\";\nvar TAB_INDEX = \"tabindex\";\nvar DISABLED = \"disabled\";\nvar ARIA_PREFIX = \"aria-\";\nvar ARIA_CONTROLS = ARIA_PREFIX + \"controls\";\nvar ARIA_CURRENT = ARIA_PREFIX + \"current\";\nvar ARIA_SELECTED = ARIA_PREFIX + \"selected\";\nvar ARIA_LABEL = ARIA_PREFIX + \"label\";\nvar ARIA_LABELLEDBY = ARIA_PREFIX + \"labelledby\";\nvar ARIA_HIDDEN = ARIA_PREFIX + \"hidden\";\nvar ARIA_ORIENTATION = ARIA_PREFIX + \"orientation\";\nvar ARIA_ROLEDESCRIPTION = ARIA_PREFIX + \"roledescription\";\nvar ARIA_LIVE = ARIA_PREFIX + \"live\";\nvar ARIA_BUSY = ARIA_PREFIX + \"busy\";\nvar ARIA_ATOMIC = ARIA_PREFIX + \"atomic\";\nvar ALL_ATTRIBUTES = [ROLE, TAB_INDEX, DISABLED, ARIA_CONTROLS, ARIA_CURRENT, ARIA_LABEL, ARIA_LABELLEDBY, ARIA_HIDDEN, ARIA_ORIENTATION, ARIA_ROLEDESCRIPTION];\nvar CLASS_PREFIX = PROJECT_CODE + \"__\";\nvar STATUS_CLASS_PREFIX = \"is-\";\nvar CLASS_ROOT = PROJECT_CODE;\nvar CLASS_TRACK = CLASS_PREFIX + \"track\";\nvar CLASS_LIST = CLASS_PREFIX + \"list\";\nvar CLASS_SLIDE = CLASS_PREFIX + \"slide\";\nvar CLASS_CLONE = CLASS_SLIDE + \"--clone\";\nvar CLASS_CONTAINER = CLASS_SLIDE + \"__container\";\nvar CLASS_ARROWS = CLASS_PREFIX + \"arrows\";\nvar CLASS_ARROW = CLASS_PREFIX + \"arrow\";\nvar CLASS_ARROW_PREV = CLASS_ARROW + \"--prev\";\nvar CLASS_ARROW_NEXT = CLASS_ARROW + \"--next\";\nvar CLASS_PAGINATION = CLASS_PREFIX + \"pagination\";\nvar CLASS_PAGINATION_PAGE = CLASS_PAGINATION + \"__page\";\nvar CLASS_PROGRESS = CLASS_PREFIX + \"progress\";\nvar CLASS_PROGRESS_BAR = CLASS_PROGRESS + \"__bar\";\nvar CLASS_TOGGLE = CLASS_PREFIX + \"toggle\";\nvar CLASS_TOGGLE_PLAY = CLASS_TOGGLE + \"__play\";\nvar CLASS_TOGGLE_PAUSE = CLASS_TOGGLE + \"__pause\";\nvar CLASS_SPINNER = CLASS_PREFIX + \"spinner\";\nvar CLASS_SR = CLASS_PREFIX + \"sr\";\nvar CLASS_INITIALIZED = STATUS_CLASS_PREFIX + \"initialized\";\nvar CLASS_ACTIVE = STATUS_CLASS_PREFIX + \"active\";\nvar CLASS_PREV = STATUS_CLASS_PREFIX + \"prev\";\nvar CLASS_NEXT = STATUS_CLASS_PREFIX + \"next\";\nvar CLASS_VISIBLE = STATUS_CLASS_PREFIX + \"visible\";\nvar CLASS_LOADING = STATUS_CLASS_PREFIX + \"loading\";\nvar CLASS_FOCUS_IN = STATUS_CLASS_PREFIX + \"focus-in\";\nvar CLASS_OVERFLOW = STATUS_CLASS_PREFIX + \"overflow\";\nvar STATUS_CLASSES = [CLASS_ACTIVE, CLASS_VISIBLE, CLASS_PREV, CLASS_NEXT, CLASS_LOADING, CLASS_FOCUS_IN, CLASS_OVERFLOW];\nvar CLASSES = {\n  slide: CLASS_SLIDE,\n  clone: CLASS_CLONE,\n  arrows: CLASS_ARROWS,\n  arrow: CLASS_ARROW,\n  prev: CLASS_ARROW_PREV,\n  next: CLASS_ARROW_NEXT,\n  pagination: CLASS_PAGINATION,\n  page: CLASS_PAGINATION_PAGE,\n  spinner: CLASS_SPINNER\n};\n\nfunction closest(from, selector) {\n  if (isFunction(from.closest)) {\n    return from.closest(selector);\n  }\n\n  var elm = from;\n\n  while (elm && elm.nodeType === 1) {\n    if (matches(elm, selector)) {\n      break;\n    }\n\n    elm = elm.parentElement;\n  }\n\n  return elm;\n}\n\nvar FRICTION = 5;\nvar LOG_INTERVAL = 200;\nvar POINTER_DOWN_EVENTS = \"touchstart mousedown\";\nvar POINTER_MOVE_EVENTS = \"touchmove mousemove\";\nvar POINTER_UP_EVENTS = \"touchend touchcancel mouseup click\";\n\nfunction Elements(Splide2, Components2, options) {\n  var _EventInterface = EventInterface(Splide2),\n      on = _EventInterface.on,\n      bind = _EventInterface.bind;\n\n  var root = Splide2.root;\n  var i18n = options.i18n;\n  var elements = {};\n  var slides = [];\n  var rootClasses = [];\n  var trackClasses = [];\n  var track;\n  var list;\n  var isUsingKey;\n\n  function setup() {\n    collect();\n    init();\n    update();\n  }\n\n  function mount() {\n    on(EVENT_REFRESH, destroy);\n    on(EVENT_REFRESH, setup);\n    on(EVENT_UPDATED, update);\n    bind(document, POINTER_DOWN_EVENTS + \" keydown\", function (e) {\n      isUsingKey = e.type === \"keydown\";\n    }, {\n      capture: true\n    });\n    bind(root, \"focusin\", function () {\n      toggleClass(root, CLASS_FOCUS_IN, !!isUsingKey);\n    });\n  }\n\n  function destroy(completely) {\n    var attrs = ALL_ATTRIBUTES.concat(\"style\");\n    empty(slides);\n    removeClass(root, rootClasses);\n    removeClass(track, trackClasses);\n    removeAttribute([track, list], attrs);\n    removeAttribute(root, completely ? attrs : [\"style\", ARIA_ROLEDESCRIPTION]);\n  }\n\n  function update() {\n    removeClass(root, rootClasses);\n    removeClass(track, trackClasses);\n    rootClasses = getClasses(CLASS_ROOT);\n    trackClasses = getClasses(CLASS_TRACK);\n    addClass(root, rootClasses);\n    addClass(track, trackClasses);\n    setAttribute(root, ARIA_LABEL, options.label);\n    setAttribute(root, ARIA_LABELLEDBY, options.labelledby);\n  }\n\n  function collect() {\n    track = find(\".\" + CLASS_TRACK);\n    list = child(track, \".\" + CLASS_LIST);\n    assert(track && list, \"A track/list element is missing.\");\n    push(slides, children(list, \".\" + CLASS_SLIDE + \":not(.\" + CLASS_CLONE + \")\"));\n    forOwn({\n      arrows: CLASS_ARROWS,\n      pagination: CLASS_PAGINATION,\n      prev: CLASS_ARROW_PREV,\n      next: CLASS_ARROW_NEXT,\n      bar: CLASS_PROGRESS_BAR,\n      toggle: CLASS_TOGGLE\n    }, function (className, key) {\n      elements[key] = find(\".\" + className);\n    });\n    assign(elements, {\n      root: root,\n      track: track,\n      list: list,\n      slides: slides\n    });\n  }\n\n  function init() {\n    var id = root.id || uniqueId(PROJECT_CODE);\n    var role = options.role;\n    root.id = id;\n    track.id = track.id || id + \"-track\";\n    list.id = list.id || id + \"-list\";\n\n    if (!getAttribute(root, ROLE) && root.tagName !== \"SECTION\" && role) {\n      setAttribute(root, ROLE, role);\n    }\n\n    setAttribute(root, ARIA_ROLEDESCRIPTION, i18n.carousel);\n    setAttribute(list, ROLE, \"presentation\");\n  }\n\n  function find(selector) {\n    var elm = query(root, selector);\n    return elm && closest(elm, \".\" + CLASS_ROOT) === root ? elm : void 0;\n  }\n\n  function getClasses(base) {\n    return [base + \"--\" + options.type, base + \"--\" + options.direction, options.drag && base + \"--draggable\", options.isNavigation && base + \"--nav\", base === CLASS_ROOT && CLASS_ACTIVE];\n  }\n\n  return assign(elements, {\n    setup: setup,\n    mount: mount,\n    destroy: destroy\n  });\n}\n\nvar SLIDE = \"slide\";\nvar LOOP = \"loop\";\nvar FADE = \"fade\";\n\nfunction Slide$1(Splide2, index, slideIndex, slide) {\n  var event = EventInterface(Splide2);\n  var on = event.on,\n      emit = event.emit,\n      bind = event.bind;\n  var Components = Splide2.Components,\n      root = Splide2.root,\n      options = Splide2.options;\n  var isNavigation = options.isNavigation,\n      updateOnMove = options.updateOnMove,\n      i18n = options.i18n,\n      pagination = options.pagination,\n      slideFocus = options.slideFocus;\n  var resolve = Components.Direction.resolve;\n  var styles = getAttribute(slide, \"style\");\n  var label = getAttribute(slide, ARIA_LABEL);\n  var isClone = slideIndex > -1;\n  var container = child(slide, \".\" + CLASS_CONTAINER);\n  var destroyed;\n\n  function mount() {\n    if (!isClone) {\n      slide.id = root.id + \"-slide\" + pad(index + 1);\n      setAttribute(slide, ROLE, pagination ? \"tabpanel\" : \"group\");\n      setAttribute(slide, ARIA_ROLEDESCRIPTION, i18n.slide);\n      setAttribute(slide, ARIA_LABEL, label || format(i18n.slideLabel, [index + 1, Splide2.length]));\n    }\n\n    listen();\n  }\n\n  function listen() {\n    bind(slide, \"click\", apply(emit, EVENT_CLICK, self));\n    bind(slide, \"keydown\", apply(emit, EVENT_SLIDE_KEYDOWN, self));\n    on([EVENT_MOVED, EVENT_SHIFTED, EVENT_SCROLLED], update);\n    on(EVENT_NAVIGATION_MOUNTED, initNavigation);\n\n    if (updateOnMove) {\n      on(EVENT_MOVE, onMove);\n    }\n  }\n\n  function destroy() {\n    destroyed = true;\n    event.destroy();\n    removeClass(slide, STATUS_CLASSES);\n    removeAttribute(slide, ALL_ATTRIBUTES);\n    setAttribute(slide, \"style\", styles);\n    setAttribute(slide, ARIA_LABEL, label || \"\");\n  }\n\n  function initNavigation() {\n    var controls = Splide2.splides.map(function (target) {\n      var Slide2 = target.splide.Components.Slides.getAt(index);\n      return Slide2 ? Slide2.slide.id : \"\";\n    }).join(\" \");\n    setAttribute(slide, ARIA_LABEL, format(i18n.slideX, (isClone ? slideIndex : index) + 1));\n    setAttribute(slide, ARIA_CONTROLS, controls);\n    setAttribute(slide, ROLE, slideFocus ? \"button\" : \"\");\n    slideFocus && removeAttribute(slide, ARIA_ROLEDESCRIPTION);\n  }\n\n  function onMove() {\n    if (!destroyed) {\n      update();\n    }\n  }\n\n  function update() {\n    if (!destroyed) {\n      var curr = Splide2.index;\n      updateActivity();\n      updateVisibility();\n      toggleClass(slide, CLASS_PREV, index === curr - 1);\n      toggleClass(slide, CLASS_NEXT, index === curr + 1);\n    }\n  }\n\n  function updateActivity() {\n    var active = isActive();\n\n    if (active !== hasClass(slide, CLASS_ACTIVE)) {\n      toggleClass(slide, CLASS_ACTIVE, active);\n      setAttribute(slide, ARIA_CURRENT, isNavigation && active || \"\");\n      emit(active ? EVENT_ACTIVE : EVENT_INACTIVE, self);\n    }\n  }\n\n  function updateVisibility() {\n    var visible = isVisible();\n    var hidden = !visible && (!isActive() || isClone);\n\n    if (!Splide2.state.is([MOVING, SCROLLING])) {\n      setAttribute(slide, ARIA_HIDDEN, hidden || \"\");\n    }\n\n    setAttribute(queryAll(slide, options.focusableNodes || \"\"), TAB_INDEX, hidden ? -1 : \"\");\n\n    if (slideFocus) {\n      setAttribute(slide, TAB_INDEX, hidden ? -1 : 0);\n    }\n\n    if (visible !== hasClass(slide, CLASS_VISIBLE)) {\n      toggleClass(slide, CLASS_VISIBLE, visible);\n      emit(visible ? EVENT_VISIBLE : EVENT_HIDDEN, self);\n    }\n\n    if (!visible && document.activeElement === slide) {\n      var Slide2 = Components.Slides.getAt(Splide2.index);\n      Slide2 && focus(Slide2.slide);\n    }\n  }\n\n  function style$1(prop, value, useContainer) {\n    style(useContainer && container || slide, prop, value);\n  }\n\n  function isActive() {\n    var curr = Splide2.index;\n    return curr === index || options.cloneStatus && curr === slideIndex;\n  }\n\n  function isVisible() {\n    if (Splide2.is(FADE)) {\n      return isActive();\n    }\n\n    var trackRect = rect(Components.Elements.track);\n    var slideRect = rect(slide);\n    var left = resolve(\"left\", true);\n    var right = resolve(\"right\", true);\n    return floor(trackRect[left]) <= ceil(slideRect[left]) && floor(slideRect[right]) <= ceil(trackRect[right]);\n  }\n\n  function isWithin(from, distance) {\n    var diff = abs(from - index);\n\n    if (!isClone && (options.rewind || Splide2.is(LOOP))) {\n      diff = min(diff, Splide2.length - diff);\n    }\n\n    return diff <= distance;\n  }\n\n  var self = {\n    index: index,\n    slideIndex: slideIndex,\n    slide: slide,\n    container: container,\n    isClone: isClone,\n    mount: mount,\n    destroy: destroy,\n    update: update,\n    style: style$1,\n    isWithin: isWithin\n  };\n  return self;\n}\n\nfunction Slides(Splide2, Components2, options) {\n  var _EventInterface2 = EventInterface(Splide2),\n      on = _EventInterface2.on,\n      emit = _EventInterface2.emit,\n      bind = _EventInterface2.bind;\n\n  var _Components2$Elements = Components2.Elements,\n      slides = _Components2$Elements.slides,\n      list = _Components2$Elements.list;\n  var Slides2 = [];\n\n  function mount() {\n    init();\n    on(EVENT_REFRESH, destroy);\n    on(EVENT_REFRESH, init);\n  }\n\n  function init() {\n    slides.forEach(function (slide, index) {\n      register(slide, index, -1);\n    });\n  }\n\n  function destroy() {\n    forEach$1(function (Slide2) {\n      Slide2.destroy();\n    });\n    empty(Slides2);\n  }\n\n  function update() {\n    forEach$1(function (Slide2) {\n      Slide2.update();\n    });\n  }\n\n  function register(slide, index, slideIndex) {\n    var object = Slide$1(Splide2, index, slideIndex, slide);\n    object.mount();\n    Slides2.push(object);\n    Slides2.sort(function (Slide1, Slide2) {\n      return Slide1.index - Slide2.index;\n    });\n  }\n\n  function get(excludeClones) {\n    return excludeClones ? filter(function (Slide2) {\n      return !Slide2.isClone;\n    }) : Slides2;\n  }\n\n  function getIn(page) {\n    var Controller = Components2.Controller;\n    var index = Controller.toIndex(page);\n    var max = Controller.hasFocus() ? 1 : options.perPage;\n    return filter(function (Slide2) {\n      return between(Slide2.index, index, index + max - 1);\n    });\n  }\n\n  function getAt(index) {\n    return filter(index)[0];\n  }\n\n  function add(items, index) {\n    forEach(items, function (slide) {\n      if (isString(slide)) {\n        slide = parseHtml(slide);\n      }\n\n      if (isHTMLElement(slide)) {\n        var ref = slides[index];\n        ref ? before(slide, ref) : append(list, slide);\n        addClass(slide, options.classes.slide);\n        observeImages(slide, apply(emit, EVENT_RESIZE));\n      }\n    });\n    emit(EVENT_REFRESH);\n  }\n\n  function remove$1(matcher) {\n    remove(filter(matcher).map(function (Slide2) {\n      return Slide2.slide;\n    }));\n    emit(EVENT_REFRESH);\n  }\n\n  function forEach$1(iteratee, excludeClones) {\n    get(excludeClones).forEach(iteratee);\n  }\n\n  function filter(matcher) {\n    return Slides2.filter(isFunction(matcher) ? matcher : function (Slide2) {\n      return isString(matcher) ? matches(Slide2.slide, matcher) : includes(toArray(matcher), Slide2.index);\n    });\n  }\n\n  function style(prop, value, useContainer) {\n    forEach$1(function (Slide2) {\n      Slide2.style(prop, value, useContainer);\n    });\n  }\n\n  function observeImages(elm, callback) {\n    var images = queryAll(elm, \"img\");\n    var length = images.length;\n\n    if (length) {\n      images.forEach(function (img) {\n        bind(img, \"load error\", function () {\n          if (! --length) {\n            callback();\n          }\n        });\n      });\n    } else {\n      callback();\n    }\n  }\n\n  function getLength(excludeClones) {\n    return excludeClones ? slides.length : Slides2.length;\n  }\n\n  function isEnough() {\n    return Slides2.length > options.perPage;\n  }\n\n  return {\n    mount: mount,\n    destroy: destroy,\n    update: update,\n    register: register,\n    get: get,\n    getIn: getIn,\n    getAt: getAt,\n    add: add,\n    remove: remove$1,\n    forEach: forEach$1,\n    filter: filter,\n    style: style,\n    getLength: getLength,\n    isEnough: isEnough\n  };\n}\n\nfunction Layout(Splide2, Components2, options) {\n  var _EventInterface3 = EventInterface(Splide2),\n      on = _EventInterface3.on,\n      bind = _EventInterface3.bind,\n      emit = _EventInterface3.emit;\n\n  var Slides = Components2.Slides;\n  var resolve = Components2.Direction.resolve;\n  var _Components2$Elements2 = Components2.Elements,\n      root = _Components2$Elements2.root,\n      track = _Components2$Elements2.track,\n      list = _Components2$Elements2.list;\n  var getAt = Slides.getAt,\n      styleSlides = Slides.style;\n  var vertical;\n  var rootRect;\n  var overflow;\n\n  function mount() {\n    init();\n    bind(window, \"resize load\", Throttle(apply(emit, EVENT_RESIZE)));\n    on([EVENT_UPDATED, EVENT_REFRESH], init);\n    on(EVENT_RESIZE, resize);\n  }\n\n  function init() {\n    vertical = options.direction === TTB;\n    style(root, \"maxWidth\", unit(options.width));\n    style(track, resolve(\"paddingLeft\"), cssPadding(false));\n    style(track, resolve(\"paddingRight\"), cssPadding(true));\n    resize(true);\n  }\n\n  function resize(force) {\n    var newRect = rect(root);\n\n    if (force || rootRect.width !== newRect.width || rootRect.height !== newRect.height) {\n      style(track, \"height\", cssTrackHeight());\n      styleSlides(resolve(\"marginRight\"), unit(options.gap));\n      styleSlides(\"width\", cssSlideWidth());\n      styleSlides(\"height\", cssSlideHeight(), true);\n      rootRect = newRect;\n      emit(EVENT_RESIZED);\n\n      if (overflow !== (overflow = isOverflow())) {\n        toggleClass(root, CLASS_OVERFLOW, overflow);\n        emit(EVENT_OVERFLOW, overflow);\n      }\n    }\n  }\n\n  function cssPadding(right) {\n    var padding = options.padding;\n    var prop = resolve(right ? \"right\" : \"left\");\n    return padding && unit(padding[prop] || (isObject(padding) ? 0 : padding)) || \"0px\";\n  }\n\n  function cssTrackHeight() {\n    var height = \"\";\n\n    if (vertical) {\n      height = cssHeight();\n      assert(height, \"height or heightRatio is missing.\");\n      height = \"calc(\" + height + \" - \" + cssPadding(false) + \" - \" + cssPadding(true) + \")\";\n    }\n\n    return height;\n  }\n\n  function cssHeight() {\n    return unit(options.height || rect(list).width * options.heightRatio);\n  }\n\n  function cssSlideWidth() {\n    return options.autoWidth ? null : unit(options.fixedWidth) || (vertical ? \"\" : cssSlideSize());\n  }\n\n  function cssSlideHeight() {\n    return unit(options.fixedHeight) || (vertical ? options.autoHeight ? null : cssSlideSize() : cssHeight());\n  }\n\n  function cssSlideSize() {\n    var gap = unit(options.gap);\n    return \"calc((100%\" + (gap && \" + \" + gap) + \")/\" + (options.perPage || 1) + (gap && \" - \" + gap) + \")\";\n  }\n\n  function listSize() {\n    return rect(list)[resolve(\"width\")];\n  }\n\n  function slideSize(index, withoutGap) {\n    var Slide = getAt(index || 0);\n    return Slide ? rect(Slide.slide)[resolve(\"width\")] + (withoutGap ? 0 : getGap()) : 0;\n  }\n\n  function totalSize(index, withoutGap) {\n    var Slide = getAt(index);\n\n    if (Slide) {\n      var right = rect(Slide.slide)[resolve(\"right\")];\n      var left = rect(list)[resolve(\"left\")];\n      return abs(right - left) + (withoutGap ? 0 : getGap());\n    }\n\n    return 0;\n  }\n\n  function sliderSize(withoutGap) {\n    return totalSize(Splide2.length - 1) - totalSize(0) + slideSize(0, withoutGap);\n  }\n\n  function getGap() {\n    var Slide = getAt(0);\n    return Slide && parseFloat(style(Slide.slide, resolve(\"marginRight\"))) || 0;\n  }\n\n  function getPadding(right) {\n    return parseFloat(style(track, resolve(\"padding\" + (right ? \"Right\" : \"Left\")))) || 0;\n  }\n\n  function isOverflow() {\n    return Splide2.is(FADE) || sliderSize(true) > listSize();\n  }\n\n  return {\n    mount: mount,\n    resize: resize,\n    listSize: listSize,\n    slideSize: slideSize,\n    sliderSize: sliderSize,\n    totalSize: totalSize,\n    getPadding: getPadding,\n    isOverflow: isOverflow\n  };\n}\n\nvar MULTIPLIER = 2;\n\nfunction Clones(Splide2, Components2, options) {\n  var event = EventInterface(Splide2);\n  var on = event.on;\n  var Elements = Components2.Elements,\n      Slides = Components2.Slides;\n  var resolve = Components2.Direction.resolve;\n  var clones = [];\n  var cloneCount;\n\n  function mount() {\n    on(EVENT_REFRESH, remount);\n    on([EVENT_UPDATED, EVENT_RESIZE], observe);\n\n    if (cloneCount = computeCloneCount()) {\n      generate(cloneCount);\n      Components2.Layout.resize(true);\n    }\n  }\n\n  function remount() {\n    destroy();\n    mount();\n  }\n\n  function destroy() {\n    remove(clones);\n    empty(clones);\n    event.destroy();\n  }\n\n  function observe() {\n    var count = computeCloneCount();\n\n    if (cloneCount !== count) {\n      if (cloneCount < count || !count) {\n        event.emit(EVENT_REFRESH);\n      }\n    }\n  }\n\n  function generate(count) {\n    var slides = Slides.get().slice();\n    var length = slides.length;\n\n    if (length) {\n      while (slides.length < count) {\n        push(slides, slides);\n      }\n\n      push(slides.slice(-count), slides.slice(0, count)).forEach(function (Slide, index) {\n        var isHead = index < count;\n        var clone = cloneDeep(Slide.slide, index);\n        isHead ? before(clone, slides[0].slide) : append(Elements.list, clone);\n        push(clones, clone);\n        Slides.register(clone, index - count + (isHead ? 0 : length), Slide.index);\n      });\n    }\n  }\n\n  function cloneDeep(elm, index) {\n    var clone = elm.cloneNode(true);\n    addClass(clone, options.classes.clone);\n    clone.id = Splide2.root.id + \"-clone\" + pad(index + 1);\n    return clone;\n  }\n\n  function computeCloneCount() {\n    var clones2 = options.clones;\n\n    if (!Splide2.is(LOOP)) {\n      clones2 = 0;\n    } else if (isUndefined(clones2)) {\n      var fixedSize = options[resolve(\"fixedWidth\")] && Components2.Layout.slideSize(0);\n      var fixedCount = fixedSize && ceil(rect(Elements.track)[resolve(\"width\")] / fixedSize);\n      clones2 = fixedCount || options[resolve(\"autoWidth\")] && Splide2.length || options.perPage * MULTIPLIER;\n    }\n\n    return clones2;\n  }\n\n  return {\n    mount: mount,\n    destroy: destroy\n  };\n}\n\nfunction Move(Splide2, Components2, options) {\n  var _EventInterface4 = EventInterface(Splide2),\n      on = _EventInterface4.on,\n      emit = _EventInterface4.emit;\n\n  var set = Splide2.state.set;\n  var _Components2$Layout = Components2.Layout,\n      slideSize = _Components2$Layout.slideSize,\n      getPadding = _Components2$Layout.getPadding,\n      totalSize = _Components2$Layout.totalSize,\n      listSize = _Components2$Layout.listSize,\n      sliderSize = _Components2$Layout.sliderSize;\n  var _Components2$Directio = Components2.Direction,\n      resolve = _Components2$Directio.resolve,\n      orient = _Components2$Directio.orient;\n  var _Components2$Elements3 = Components2.Elements,\n      list = _Components2$Elements3.list,\n      track = _Components2$Elements3.track;\n  var Transition;\n\n  function mount() {\n    Transition = Components2.Transition;\n    on([EVENT_MOUNTED, EVENT_RESIZED, EVENT_UPDATED, EVENT_REFRESH], reposition);\n  }\n\n  function reposition() {\n    if (!Components2.Controller.isBusy()) {\n      Components2.Scroll.cancel();\n      jump(Splide2.index);\n      Components2.Slides.update();\n    }\n  }\n\n  function move(dest, index, prev, callback) {\n    if (dest !== index && canShift(dest > prev)) {\n      cancel();\n      translate(shift(getPosition(), dest > prev), true);\n    }\n\n    set(MOVING);\n    emit(EVENT_MOVE, index, prev, dest);\n    Transition.start(index, function () {\n      set(IDLE);\n      emit(EVENT_MOVED, index, prev, dest);\n      callback && callback();\n    });\n  }\n\n  function jump(index) {\n    translate(toPosition(index, true));\n  }\n\n  function translate(position, preventLoop) {\n    if (!Splide2.is(FADE)) {\n      var destination = preventLoop ? position : loop(position);\n      style(list, \"transform\", \"translate\" + resolve(\"X\") + \"(\" + destination + \"px)\");\n      position !== destination && emit(EVENT_SHIFTED);\n    }\n  }\n\n  function loop(position) {\n    if (Splide2.is(LOOP)) {\n      var index = toIndex(position);\n      var exceededMax = index > Components2.Controller.getEnd();\n      var exceededMin = index < 0;\n\n      if (exceededMin || exceededMax) {\n        position = shift(position, exceededMax);\n      }\n    }\n\n    return position;\n  }\n\n  function shift(position, backwards) {\n    var excess = position - getLimit(backwards);\n    var size = sliderSize();\n    position -= orient(size * (ceil(abs(excess) / size) || 1)) * (backwards ? 1 : -1);\n    return position;\n  }\n\n  function cancel() {\n    translate(getPosition(), true);\n    Transition.cancel();\n  }\n\n  function toIndex(position) {\n    var Slides = Components2.Slides.get();\n    var index = 0;\n    var minDistance = Infinity;\n\n    for (var i = 0; i < Slides.length; i++) {\n      var slideIndex = Slides[i].index;\n      var distance = abs(toPosition(slideIndex, true) - position);\n\n      if (distance <= minDistance) {\n        minDistance = distance;\n        index = slideIndex;\n      } else {\n        break;\n      }\n    }\n\n    return index;\n  }\n\n  function toPosition(index, trimming) {\n    var position = orient(totalSize(index - 1) - offset(index));\n    return trimming ? trim(position) : position;\n  }\n\n  function getPosition() {\n    var left = resolve(\"left\");\n    return rect(list)[left] - rect(track)[left] + orient(getPadding(false));\n  }\n\n  function trim(position) {\n    if (options.trimSpace && Splide2.is(SLIDE)) {\n      position = clamp(position, 0, orient(sliderSize(true) - listSize()));\n    }\n\n    return position;\n  }\n\n  function offset(index) {\n    var focus = options.focus;\n    return focus === \"center\" ? (listSize() - slideSize(index, true)) / 2 : +focus * slideSize(index) || 0;\n  }\n\n  function getLimit(max) {\n    return toPosition(max ? Components2.Controller.getEnd() : 0, !!options.trimSpace);\n  }\n\n  function canShift(backwards) {\n    var shifted = orient(shift(getPosition(), backwards));\n    return backwards ? shifted >= 0 : shifted <= list[resolve(\"scrollWidth\")] - rect(track)[resolve(\"width\")];\n  }\n\n  function exceededLimit(max, position) {\n    position = isUndefined(position) ? getPosition() : position;\n    var exceededMin = max !== true && orient(position) < orient(getLimit(false));\n    var exceededMax = max !== false && orient(position) > orient(getLimit(true));\n    return exceededMin || exceededMax;\n  }\n\n  return {\n    mount: mount,\n    move: move,\n    jump: jump,\n    translate: translate,\n    shift: shift,\n    cancel: cancel,\n    toIndex: toIndex,\n    toPosition: toPosition,\n    getPosition: getPosition,\n    getLimit: getLimit,\n    exceededLimit: exceededLimit,\n    reposition: reposition\n  };\n}\n\nfunction Controller(Splide2, Components2, options) {\n  var _EventInterface5 = EventInterface(Splide2),\n      on = _EventInterface5.on,\n      emit = _EventInterface5.emit;\n\n  var Move = Components2.Move;\n  var getPosition = Move.getPosition,\n      getLimit = Move.getLimit,\n      toPosition = Move.toPosition;\n  var _Components2$Slides = Components2.Slides,\n      isEnough = _Components2$Slides.isEnough,\n      getLength = _Components2$Slides.getLength;\n  var omitEnd = options.omitEnd;\n  var isLoop = Splide2.is(LOOP);\n  var isSlide = Splide2.is(SLIDE);\n  var getNext = apply(getAdjacent, false);\n  var getPrev = apply(getAdjacent, true);\n  var currIndex = options.start || 0;\n  var endIndex;\n  var prevIndex = currIndex;\n  var slideCount;\n  var perMove;\n  var perPage;\n\n  function mount() {\n    init();\n    on([EVENT_UPDATED, EVENT_REFRESH, EVENT_END_INDEX_CHANGED], init);\n    on(EVENT_RESIZED, onResized);\n  }\n\n  function init() {\n    slideCount = getLength(true);\n    perMove = options.perMove;\n    perPage = options.perPage;\n    endIndex = getEnd();\n    var index = clamp(currIndex, 0, omitEnd ? endIndex : slideCount - 1);\n\n    if (index !== currIndex) {\n      currIndex = index;\n      Move.reposition();\n    }\n  }\n\n  function onResized() {\n    if (endIndex !== getEnd()) {\n      emit(EVENT_END_INDEX_CHANGED);\n    }\n  }\n\n  function go(control, allowSameIndex, callback) {\n    if (!isBusy()) {\n      var dest = parse(control);\n      var index = loop(dest);\n\n      if (index > -1 && (allowSameIndex || index !== currIndex)) {\n        setIndex(index);\n        Move.move(dest, index, prevIndex, callback);\n      }\n    }\n  }\n\n  function scroll(destination, duration, snap, callback) {\n    Components2.Scroll.scroll(destination, duration, snap, function () {\n      var index = loop(Move.toIndex(getPosition()));\n      setIndex(omitEnd ? min(index, endIndex) : index);\n      callback && callback();\n    });\n  }\n\n  function parse(control) {\n    var index = currIndex;\n\n    if (isString(control)) {\n      var _ref = control.match(/([+\\-<>])(\\d+)?/) || [],\n          indicator = _ref[1],\n          number = _ref[2];\n\n      if (indicator === \"+\" || indicator === \"-\") {\n        index = computeDestIndex(currIndex + +(\"\" + indicator + (+number || 1)), currIndex);\n      } else if (indicator === \">\") {\n        index = number ? toIndex(+number) : getNext(true);\n      } else if (indicator === \"<\") {\n        index = getPrev(true);\n      }\n    } else {\n      index = isLoop ? control : clamp(control, 0, endIndex);\n    }\n\n    return index;\n  }\n\n  function getAdjacent(prev, destination) {\n    var number = perMove || (hasFocus() ? 1 : perPage);\n    var dest = computeDestIndex(currIndex + number * (prev ? -1 : 1), currIndex, !(perMove || hasFocus()));\n\n    if (dest === -1 && isSlide) {\n      if (!approximatelyEqual(getPosition(), getLimit(!prev), 1)) {\n        return prev ? 0 : endIndex;\n      }\n    }\n\n    return destination ? dest : loop(dest);\n  }\n\n  function computeDestIndex(dest, from, snapPage) {\n    if (isEnough() || hasFocus()) {\n      var index = computeMovableDestIndex(dest);\n\n      if (index !== dest) {\n        from = dest;\n        dest = index;\n        snapPage = false;\n      }\n\n      if (dest < 0 || dest > endIndex) {\n        if (!perMove && (between(0, dest, from, true) || between(endIndex, from, dest, true))) {\n          dest = toIndex(toPage(dest));\n        } else {\n          if (isLoop) {\n            dest = snapPage ? dest < 0 ? -(slideCount % perPage || perPage) : slideCount : dest;\n          } else if (options.rewind) {\n            dest = dest < 0 ? endIndex : 0;\n          } else {\n            dest = -1;\n          }\n        }\n      } else {\n        if (snapPage && dest !== from) {\n          dest = toIndex(toPage(from) + (dest < from ? -1 : 1));\n        }\n      }\n    } else {\n      dest = -1;\n    }\n\n    return dest;\n  }\n\n  function computeMovableDestIndex(dest) {\n    if (isSlide && options.trimSpace === \"move\" && dest !== currIndex) {\n      var position = getPosition();\n\n      while (position === toPosition(dest, true) && between(dest, 0, Splide2.length - 1, !options.rewind)) {\n        dest < currIndex ? --dest : ++dest;\n      }\n    }\n\n    return dest;\n  }\n\n  function loop(index) {\n    return isLoop ? (index + slideCount) % slideCount || 0 : index;\n  }\n\n  function getEnd() {\n    var end = slideCount - (hasFocus() || isLoop && perMove ? 1 : perPage);\n\n    while (omitEnd && end-- > 0) {\n      if (toPosition(slideCount - 1, true) !== toPosition(end, true)) {\n        end++;\n        break;\n      }\n    }\n\n    return clamp(end, 0, slideCount - 1);\n  }\n\n  function toIndex(page) {\n    return clamp(hasFocus() ? page : perPage * page, 0, endIndex);\n  }\n\n  function toPage(index) {\n    return hasFocus() ? min(index, endIndex) : floor((index >= endIndex ? slideCount - 1 : index) / perPage);\n  }\n\n  function toDest(destination) {\n    var closest = Move.toIndex(destination);\n    return isSlide ? clamp(closest, 0, endIndex) : closest;\n  }\n\n  function setIndex(index) {\n    if (index !== currIndex) {\n      prevIndex = currIndex;\n      currIndex = index;\n    }\n  }\n\n  function getIndex(prev) {\n    return prev ? prevIndex : currIndex;\n  }\n\n  function hasFocus() {\n    return !isUndefined(options.focus) || options.isNavigation;\n  }\n\n  function isBusy() {\n    return Splide2.state.is([MOVING, SCROLLING]) && !!options.waitForTransition;\n  }\n\n  return {\n    mount: mount,\n    go: go,\n    scroll: scroll,\n    getNext: getNext,\n    getPrev: getPrev,\n    getAdjacent: getAdjacent,\n    getEnd: getEnd,\n    setIndex: setIndex,\n    getIndex: getIndex,\n    toIndex: toIndex,\n    toPage: toPage,\n    toDest: toDest,\n    hasFocus: hasFocus,\n    isBusy: isBusy\n  };\n}\n\nvar XML_NAME_SPACE = \"http://www.w3.org/2000/svg\";\nvar PATH = \"m15.5 0.932-4.3 4.38 14.5 14.6-14.5 14.5 4.3 4.4 14.6-14.6 4.4-4.3-4.4-4.4-14.6-14.6z\";\nvar SIZE = 40;\n\nfunction Arrows(Splide2, Components2, options) {\n  var event = EventInterface(Splide2);\n  var on = event.on,\n      bind = event.bind,\n      emit = event.emit;\n  var classes = options.classes,\n      i18n = options.i18n;\n  var Elements = Components2.Elements,\n      Controller = Components2.Controller;\n  var placeholder = Elements.arrows,\n      track = Elements.track;\n  var wrapper = placeholder;\n  var prev = Elements.prev;\n  var next = Elements.next;\n  var created;\n  var wrapperClasses;\n  var arrows = {};\n\n  function mount() {\n    init();\n    on(EVENT_UPDATED, remount);\n  }\n\n  function remount() {\n    destroy();\n    mount();\n  }\n\n  function init() {\n    var enabled = options.arrows;\n\n    if (enabled && !(prev && next)) {\n      createArrows();\n    }\n\n    if (prev && next) {\n      assign(arrows, {\n        prev: prev,\n        next: next\n      });\n      display(wrapper, enabled ? \"\" : \"none\");\n      addClass(wrapper, wrapperClasses = CLASS_ARROWS + \"--\" + options.direction);\n\n      if (enabled) {\n        listen();\n        update();\n        setAttribute([prev, next], ARIA_CONTROLS, track.id);\n        emit(EVENT_ARROWS_MOUNTED, prev, next);\n      }\n    }\n  }\n\n  function destroy() {\n    event.destroy();\n    removeClass(wrapper, wrapperClasses);\n\n    if (created) {\n      remove(placeholder ? [prev, next] : wrapper);\n      prev = next = null;\n    } else {\n      removeAttribute([prev, next], ALL_ATTRIBUTES);\n    }\n  }\n\n  function listen() {\n    on([EVENT_MOUNTED, EVENT_MOVED, EVENT_REFRESH, EVENT_SCROLLED, EVENT_END_INDEX_CHANGED], update);\n    bind(next, \"click\", apply(go, \">\"));\n    bind(prev, \"click\", apply(go, \"<\"));\n  }\n\n  function go(control) {\n    Controller.go(control, true);\n  }\n\n  function createArrows() {\n    wrapper = placeholder || create(\"div\", classes.arrows);\n    prev = createArrow(true);\n    next = createArrow(false);\n    created = true;\n    append(wrapper, [prev, next]);\n    !placeholder && before(wrapper, track);\n  }\n\n  function createArrow(prev2) {\n    var arrow = \"<button class=\\\"\" + classes.arrow + \" \" + (prev2 ? classes.prev : classes.next) + \"\\\" type=\\\"button\\\"><svg xmlns=\\\"\" + XML_NAME_SPACE + \"\\\" viewBox=\\\"0 0 \" + SIZE + \" \" + SIZE + \"\\\" width=\\\"\" + SIZE + \"\\\" height=\\\"\" + SIZE + \"\\\" focusable=\\\"false\\\"><path d=\\\"\" + (options.arrowPath || PATH) + \"\\\" />\";\n    return parseHtml(arrow);\n  }\n\n  function update() {\n    if (prev && next) {\n      var index = Splide2.index;\n      var prevIndex = Controller.getPrev();\n      var nextIndex = Controller.getNext();\n      var prevLabel = prevIndex > -1 && index < prevIndex ? i18n.last : i18n.prev;\n      var nextLabel = nextIndex > -1 && index > nextIndex ? i18n.first : i18n.next;\n      prev.disabled = prevIndex < 0;\n      next.disabled = nextIndex < 0;\n      setAttribute(prev, ARIA_LABEL, prevLabel);\n      setAttribute(next, ARIA_LABEL, nextLabel);\n      emit(EVENT_ARROWS_UPDATED, prev, next, prevIndex, nextIndex);\n    }\n  }\n\n  return {\n    arrows: arrows,\n    mount: mount,\n    destroy: destroy,\n    update: update\n  };\n}\n\nvar INTERVAL_DATA_ATTRIBUTE = DATA_ATTRIBUTE + \"-interval\";\n\nfunction Autoplay(Splide2, Components2, options) {\n  var _EventInterface6 = EventInterface(Splide2),\n      on = _EventInterface6.on,\n      bind = _EventInterface6.bind,\n      emit = _EventInterface6.emit;\n\n  var interval = RequestInterval(options.interval, Splide2.go.bind(Splide2, \">\"), onAnimationFrame);\n  var isPaused = interval.isPaused;\n  var Elements = Components2.Elements,\n      _Components2$Elements4 = Components2.Elements,\n      root = _Components2$Elements4.root,\n      toggle = _Components2$Elements4.toggle;\n  var autoplay = options.autoplay;\n  var hovered;\n  var focused;\n  var stopped = autoplay === \"pause\";\n\n  function mount() {\n    if (autoplay) {\n      listen();\n      toggle && setAttribute(toggle, ARIA_CONTROLS, Elements.track.id);\n      stopped || play();\n      update();\n    }\n  }\n\n  function listen() {\n    if (options.pauseOnHover) {\n      bind(root, \"mouseenter mouseleave\", function (e) {\n        hovered = e.type === \"mouseenter\";\n        autoToggle();\n      });\n    }\n\n    if (options.pauseOnFocus) {\n      bind(root, \"focusin focusout\", function (e) {\n        focused = e.type === \"focusin\";\n        autoToggle();\n      });\n    }\n\n    if (toggle) {\n      bind(toggle, \"click\", function () {\n        stopped ? play() : pause(true);\n      });\n    }\n\n    on([EVENT_MOVE, EVENT_SCROLL, EVENT_REFRESH], interval.rewind);\n    on(EVENT_MOVE, onMove);\n  }\n\n  function play() {\n    if (isPaused() && Components2.Slides.isEnough()) {\n      interval.start(!options.resetProgress);\n      focused = hovered = stopped = false;\n      update();\n      emit(EVENT_AUTOPLAY_PLAY);\n    }\n  }\n\n  function pause(stop) {\n    if (stop === void 0) {\n      stop = true;\n    }\n\n    stopped = !!stop;\n    update();\n\n    if (!isPaused()) {\n      interval.pause();\n      emit(EVENT_AUTOPLAY_PAUSE);\n    }\n  }\n\n  function autoToggle() {\n    if (!stopped) {\n      hovered || focused ? pause(false) : play();\n    }\n  }\n\n  function update() {\n    if (toggle) {\n      toggleClass(toggle, CLASS_ACTIVE, !stopped);\n      setAttribute(toggle, ARIA_LABEL, options.i18n[stopped ? \"play\" : \"pause\"]);\n    }\n  }\n\n  function onAnimationFrame(rate) {\n    var bar = Elements.bar;\n    bar && style(bar, \"width\", rate * 100 + \"%\");\n    emit(EVENT_AUTOPLAY_PLAYING, rate);\n  }\n\n  function onMove(index) {\n    var Slide = Components2.Slides.getAt(index);\n    interval.set(Slide && +getAttribute(Slide.slide, INTERVAL_DATA_ATTRIBUTE) || options.interval);\n  }\n\n  return {\n    mount: mount,\n    destroy: interval.cancel,\n    play: play,\n    pause: pause,\n    isPaused: isPaused\n  };\n}\n\nfunction Cover(Splide2, Components2, options) {\n  var _EventInterface7 = EventInterface(Splide2),\n      on = _EventInterface7.on;\n\n  function mount() {\n    if (options.cover) {\n      on(EVENT_LAZYLOAD_LOADED, apply(toggle, true));\n      on([EVENT_MOUNTED, EVENT_UPDATED, EVENT_REFRESH], apply(cover, true));\n    }\n  }\n\n  function cover(cover2) {\n    Components2.Slides.forEach(function (Slide) {\n      var img = child(Slide.container || Slide.slide, \"img\");\n\n      if (img && img.src) {\n        toggle(cover2, img, Slide);\n      }\n    });\n  }\n\n  function toggle(cover2, img, Slide) {\n    Slide.style(\"background\", cover2 ? \"center/cover no-repeat url(\\\"\" + img.src + \"\\\")\" : \"\", true);\n    display(img, cover2 ? \"none\" : \"\");\n  }\n\n  return {\n    mount: mount,\n    destroy: apply(cover, false)\n  };\n}\n\nvar BOUNCE_DIFF_THRESHOLD = 10;\nvar BOUNCE_DURATION = 600;\nvar FRICTION_FACTOR = 0.6;\nvar BASE_VELOCITY = 1.5;\nvar MIN_DURATION = 800;\n\nfunction Scroll(Splide2, Components2, options) {\n  var _EventInterface8 = EventInterface(Splide2),\n      on = _EventInterface8.on,\n      emit = _EventInterface8.emit;\n\n  var set = Splide2.state.set;\n  var Move = Components2.Move;\n  var getPosition = Move.getPosition,\n      getLimit = Move.getLimit,\n      exceededLimit = Move.exceededLimit,\n      translate = Move.translate;\n  var isSlide = Splide2.is(SLIDE);\n  var interval;\n  var callback;\n  var friction = 1;\n\n  function mount() {\n    on(EVENT_MOVE, clear);\n    on([EVENT_UPDATED, EVENT_REFRESH], cancel);\n  }\n\n  function scroll(destination, duration, snap, onScrolled, noConstrain) {\n    var from = getPosition();\n    clear();\n\n    if (snap && (!isSlide || !exceededLimit())) {\n      var size = Components2.Layout.sliderSize();\n      var offset = sign(destination) * size * floor(abs(destination) / size) || 0;\n      destination = Move.toPosition(Components2.Controller.toDest(destination % size)) + offset;\n    }\n\n    var noDistance = approximatelyEqual(from, destination, 1);\n    friction = 1;\n    duration = noDistance ? 0 : duration || max(abs(destination - from) / BASE_VELOCITY, MIN_DURATION);\n    callback = onScrolled;\n    interval = RequestInterval(duration, onEnd, apply(update, from, destination, noConstrain), 1);\n    set(SCROLLING);\n    emit(EVENT_SCROLL);\n    interval.start();\n  }\n\n  function onEnd() {\n    set(IDLE);\n    callback && callback();\n    emit(EVENT_SCROLLED);\n  }\n\n  function update(from, to, noConstrain, rate) {\n    var position = getPosition();\n    var target = from + (to - from) * easing(rate);\n    var diff = (target - position) * friction;\n    translate(position + diff);\n\n    if (isSlide && !noConstrain && exceededLimit()) {\n      friction *= FRICTION_FACTOR;\n\n      if (abs(diff) < BOUNCE_DIFF_THRESHOLD) {\n        scroll(getLimit(exceededLimit(true)), BOUNCE_DURATION, false, callback, true);\n      }\n    }\n  }\n\n  function clear() {\n    if (interval) {\n      interval.cancel();\n    }\n  }\n\n  function cancel() {\n    if (interval && !interval.isPaused()) {\n      clear();\n      onEnd();\n    }\n  }\n\n  function easing(t) {\n    var easingFunc = options.easingFunc;\n    return easingFunc ? easingFunc(t) : 1 - Math.pow(1 - t, 4);\n  }\n\n  return {\n    mount: mount,\n    destroy: clear,\n    scroll: scroll,\n    cancel: cancel\n  };\n}\n\nvar SCROLL_LISTENER_OPTIONS = {\n  passive: false,\n  capture: true\n};\n\nfunction Drag(Splide2, Components2, options) {\n  var _EventInterface9 = EventInterface(Splide2),\n      on = _EventInterface9.on,\n      emit = _EventInterface9.emit,\n      bind = _EventInterface9.bind,\n      unbind = _EventInterface9.unbind;\n\n  var state = Splide2.state;\n  var Move = Components2.Move,\n      Scroll = Components2.Scroll,\n      Controller = Components2.Controller,\n      track = Components2.Elements.track,\n      reduce = Components2.Media.reduce;\n  var _Components2$Directio2 = Components2.Direction,\n      resolve = _Components2$Directio2.resolve,\n      orient = _Components2$Directio2.orient;\n  var getPosition = Move.getPosition,\n      exceededLimit = Move.exceededLimit;\n  var basePosition;\n  var baseEvent;\n  var prevBaseEvent;\n  var isFree;\n  var dragging;\n  var exceeded = false;\n  var clickPrevented;\n  var disabled;\n  var target;\n\n  function mount() {\n    bind(track, POINTER_MOVE_EVENTS, noop, SCROLL_LISTENER_OPTIONS);\n    bind(track, POINTER_UP_EVENTS, noop, SCROLL_LISTENER_OPTIONS);\n    bind(track, POINTER_DOWN_EVENTS, onPointerDown, SCROLL_LISTENER_OPTIONS);\n    bind(track, \"click\", onClick, {\n      capture: true\n    });\n    bind(track, \"dragstart\", prevent);\n    on([EVENT_MOUNTED, EVENT_UPDATED], init);\n  }\n\n  function init() {\n    var drag = options.drag;\n    disable(!drag);\n    isFree = drag === \"free\";\n  }\n\n  function onPointerDown(e) {\n    clickPrevented = false;\n\n    if (!disabled) {\n      var isTouch = isTouchEvent(e);\n\n      if (isDraggable(e.target) && (isTouch || !e.button)) {\n        if (!Controller.isBusy()) {\n          target = isTouch ? track : window;\n          dragging = state.is([MOVING, SCROLLING]);\n          prevBaseEvent = null;\n          bind(target, POINTER_MOVE_EVENTS, onPointerMove, SCROLL_LISTENER_OPTIONS);\n          bind(target, POINTER_UP_EVENTS, onPointerUp, SCROLL_LISTENER_OPTIONS);\n          Move.cancel();\n          Scroll.cancel();\n          save(e);\n        } else {\n          prevent(e, true);\n        }\n      }\n    }\n  }\n\n  function onPointerMove(e) {\n    if (!state.is(DRAGGING)) {\n      state.set(DRAGGING);\n      emit(EVENT_DRAG);\n    }\n\n    if (e.cancelable) {\n      if (dragging) {\n        Move.translate(basePosition + constrain(diffCoord(e)));\n        var expired = diffTime(e) > LOG_INTERVAL;\n        var hasExceeded = exceeded !== (exceeded = exceededLimit());\n\n        if (expired || hasExceeded) {\n          save(e);\n        }\n\n        clickPrevented = true;\n        emit(EVENT_DRAGGING);\n        prevent(e);\n      } else if (isSliderDirection(e)) {\n        dragging = shouldStart(e);\n        prevent(e);\n      }\n    }\n  }\n\n  function onPointerUp(e) {\n    if (state.is(DRAGGING)) {\n      state.set(IDLE);\n      emit(EVENT_DRAGGED);\n    }\n\n    if (dragging) {\n      move(e);\n      prevent(e);\n    }\n\n    unbind(target, POINTER_MOVE_EVENTS, onPointerMove);\n    unbind(target, POINTER_UP_EVENTS, onPointerUp);\n    dragging = false;\n  }\n\n  function onClick(e) {\n    if (!disabled && clickPrevented) {\n      prevent(e, true);\n    }\n  }\n\n  function save(e) {\n    prevBaseEvent = baseEvent;\n    baseEvent = e;\n    basePosition = getPosition();\n  }\n\n  function move(e) {\n    var velocity = computeVelocity(e);\n    var destination = computeDestination(velocity);\n    var rewind = options.rewind && options.rewindByDrag;\n    reduce(false);\n\n    if (isFree) {\n      Controller.scroll(destination, 0, options.snap);\n    } else if (Splide2.is(FADE)) {\n      Controller.go(orient(sign(velocity)) < 0 ? rewind ? \"<\" : \"-\" : rewind ? \">\" : \"+\");\n    } else if (Splide2.is(SLIDE) && exceeded && rewind) {\n      Controller.go(exceededLimit(true) ? \">\" : \"<\");\n    } else {\n      Controller.go(Controller.toDest(destination), true);\n    }\n\n    reduce(true);\n  }\n\n  function shouldStart(e) {\n    var thresholds = options.dragMinThreshold;\n    var isObj = isObject(thresholds);\n    var mouse = isObj && thresholds.mouse || 0;\n    var touch = (isObj ? thresholds.touch : +thresholds) || 10;\n    return abs(diffCoord(e)) > (isTouchEvent(e) ? touch : mouse);\n  }\n\n  function isSliderDirection(e) {\n    return abs(diffCoord(e)) > abs(diffCoord(e, true));\n  }\n\n  function computeVelocity(e) {\n    if (Splide2.is(LOOP) || !exceeded) {\n      var time = diffTime(e);\n\n      if (time && time < LOG_INTERVAL) {\n        return diffCoord(e) / time;\n      }\n    }\n\n    return 0;\n  }\n\n  function computeDestination(velocity) {\n    return getPosition() + sign(velocity) * min(abs(velocity) * (options.flickPower || 600), isFree ? Infinity : Components2.Layout.listSize() * (options.flickMaxPages || 1));\n  }\n\n  function diffCoord(e, orthogonal) {\n    return coordOf(e, orthogonal) - coordOf(getBaseEvent(e), orthogonal);\n  }\n\n  function diffTime(e) {\n    return timeOf(e) - timeOf(getBaseEvent(e));\n  }\n\n  function getBaseEvent(e) {\n    return baseEvent === e && prevBaseEvent || baseEvent;\n  }\n\n  function coordOf(e, orthogonal) {\n    return (isTouchEvent(e) ? e.changedTouches[0] : e)[\"page\" + resolve(orthogonal ? \"Y\" : \"X\")];\n  }\n\n  function constrain(diff) {\n    return diff / (exceeded && Splide2.is(SLIDE) ? FRICTION : 1);\n  }\n\n  function isDraggable(target2) {\n    var noDrag = options.noDrag;\n    return !matches(target2, \".\" + CLASS_PAGINATION_PAGE + \", .\" + CLASS_ARROW) && (!noDrag || !matches(target2, noDrag));\n  }\n\n  function isTouchEvent(e) {\n    return typeof TouchEvent !== \"undefined\" && e instanceof TouchEvent;\n  }\n\n  function isDragging() {\n    return dragging;\n  }\n\n  function disable(value) {\n    disabled = value;\n  }\n\n  return {\n    mount: mount,\n    disable: disable,\n    isDragging: isDragging\n  };\n}\n\nvar NORMALIZATION_MAP = {\n  Spacebar: \" \",\n  Right: ARROW_RIGHT,\n  Left: ARROW_LEFT,\n  Up: ARROW_UP,\n  Down: ARROW_DOWN\n};\n\nfunction normalizeKey(key) {\n  key = isString(key) ? key : key.key;\n  return NORMALIZATION_MAP[key] || key;\n}\n\nvar KEYBOARD_EVENT = \"keydown\";\n\nfunction Keyboard(Splide2, Components2, options) {\n  var _EventInterface10 = EventInterface(Splide2),\n      on = _EventInterface10.on,\n      bind = _EventInterface10.bind,\n      unbind = _EventInterface10.unbind;\n\n  var root = Splide2.root;\n  var resolve = Components2.Direction.resolve;\n  var target;\n  var disabled;\n\n  function mount() {\n    init();\n    on(EVENT_UPDATED, destroy);\n    on(EVENT_UPDATED, init);\n    on(EVENT_MOVE, onMove);\n  }\n\n  function init() {\n    var keyboard = options.keyboard;\n\n    if (keyboard) {\n      target = keyboard === \"global\" ? window : root;\n      bind(target, KEYBOARD_EVENT, onKeydown);\n    }\n  }\n\n  function destroy() {\n    unbind(target, KEYBOARD_EVENT);\n  }\n\n  function disable(value) {\n    disabled = value;\n  }\n\n  function onMove() {\n    var _disabled = disabled;\n    disabled = true;\n    nextTick(function () {\n      disabled = _disabled;\n    });\n  }\n\n  function onKeydown(e) {\n    if (!disabled) {\n      var key = normalizeKey(e);\n\n      if (key === resolve(ARROW_LEFT)) {\n        Splide2.go(\"<\");\n      } else if (key === resolve(ARROW_RIGHT)) {\n        Splide2.go(\">\");\n      }\n    }\n  }\n\n  return {\n    mount: mount,\n    destroy: destroy,\n    disable: disable\n  };\n}\n\nvar SRC_DATA_ATTRIBUTE = DATA_ATTRIBUTE + \"-lazy\";\nvar SRCSET_DATA_ATTRIBUTE = SRC_DATA_ATTRIBUTE + \"-srcset\";\nvar IMAGE_SELECTOR = \"[\" + SRC_DATA_ATTRIBUTE + \"], [\" + SRCSET_DATA_ATTRIBUTE + \"]\";\n\nfunction LazyLoad(Splide2, Components2, options) {\n  var _EventInterface11 = EventInterface(Splide2),\n      on = _EventInterface11.on,\n      off = _EventInterface11.off,\n      bind = _EventInterface11.bind,\n      emit = _EventInterface11.emit;\n\n  var isSequential = options.lazyLoad === \"sequential\";\n  var events = [EVENT_MOVED, EVENT_SCROLLED];\n  var entries = [];\n\n  function mount() {\n    if (options.lazyLoad) {\n      init();\n      on(EVENT_REFRESH, init);\n    }\n  }\n\n  function init() {\n    empty(entries);\n    register();\n\n    if (isSequential) {\n      loadNext();\n    } else {\n      off(events);\n      on(events, check);\n      check();\n    }\n  }\n\n  function register() {\n    Components2.Slides.forEach(function (Slide) {\n      queryAll(Slide.slide, IMAGE_SELECTOR).forEach(function (img) {\n        var src = getAttribute(img, SRC_DATA_ATTRIBUTE);\n        var srcset = getAttribute(img, SRCSET_DATA_ATTRIBUTE);\n\n        if (src !== img.src || srcset !== img.srcset) {\n          var className = options.classes.spinner;\n          var parent = img.parentElement;\n          var spinner = child(parent, \".\" + className) || create(\"span\", className, parent);\n          entries.push([img, Slide, spinner]);\n          img.src || display(img, \"none\");\n        }\n      });\n    });\n  }\n\n  function check() {\n    entries = entries.filter(function (data) {\n      var distance = options.perPage * ((options.preloadPages || 1) + 1) - 1;\n      return data[1].isWithin(Splide2.index, distance) ? load(data) : true;\n    });\n    entries.length || off(events);\n  }\n\n  function load(data) {\n    var img = data[0];\n    addClass(data[1].slide, CLASS_LOADING);\n    bind(img, \"load error\", apply(onLoad, data));\n    setAttribute(img, \"src\", getAttribute(img, SRC_DATA_ATTRIBUTE));\n    setAttribute(img, \"srcset\", getAttribute(img, SRCSET_DATA_ATTRIBUTE));\n    removeAttribute(img, SRC_DATA_ATTRIBUTE);\n    removeAttribute(img, SRCSET_DATA_ATTRIBUTE);\n  }\n\n  function onLoad(data, e) {\n    var img = data[0],\n        Slide = data[1];\n    removeClass(Slide.slide, CLASS_LOADING);\n\n    if (e.type !== \"error\") {\n      remove(data[2]);\n      display(img, \"\");\n      emit(EVENT_LAZYLOAD_LOADED, img, Slide);\n      emit(EVENT_RESIZE);\n    }\n\n    isSequential && loadNext();\n  }\n\n  function loadNext() {\n    entries.length && load(entries.shift());\n  }\n\n  return {\n    mount: mount,\n    destroy: apply(empty, entries),\n    check: check\n  };\n}\n\nfunction Pagination(Splide2, Components2, options) {\n  var event = EventInterface(Splide2);\n  var on = event.on,\n      emit = event.emit,\n      bind = event.bind;\n  var Slides = Components2.Slides,\n      Elements = Components2.Elements,\n      Controller = Components2.Controller;\n  var hasFocus = Controller.hasFocus,\n      getIndex = Controller.getIndex,\n      go = Controller.go;\n  var resolve = Components2.Direction.resolve;\n  var placeholder = Elements.pagination;\n  var items = [];\n  var list;\n  var paginationClasses;\n\n  function mount() {\n    destroy();\n    on([EVENT_UPDATED, EVENT_REFRESH, EVENT_END_INDEX_CHANGED], mount);\n    var enabled = options.pagination;\n    placeholder && display(placeholder, enabled ? \"\" : \"none\");\n\n    if (enabled) {\n      on([EVENT_MOVE, EVENT_SCROLL, EVENT_SCROLLED], update);\n      createPagination();\n      update();\n      emit(EVENT_PAGINATION_MOUNTED, {\n        list: list,\n        items: items\n      }, getAt(Splide2.index));\n    }\n  }\n\n  function destroy() {\n    if (list) {\n      remove(placeholder ? slice(list.children) : list);\n      removeClass(list, paginationClasses);\n      empty(items);\n      list = null;\n    }\n\n    event.destroy();\n  }\n\n  function createPagination() {\n    var length = Splide2.length;\n    var classes = options.classes,\n        i18n = options.i18n,\n        perPage = options.perPage;\n    var max = hasFocus() ? Controller.getEnd() + 1 : ceil(length / perPage);\n    list = placeholder || create(\"ul\", classes.pagination, Elements.track.parentElement);\n    addClass(list, paginationClasses = CLASS_PAGINATION + \"--\" + getDirection());\n    setAttribute(list, ROLE, \"tablist\");\n    setAttribute(list, ARIA_LABEL, i18n.select);\n    setAttribute(list, ARIA_ORIENTATION, getDirection() === TTB ? \"vertical\" : \"\");\n\n    for (var i = 0; i < max; i++) {\n      var li = create(\"li\", null, list);\n      var button = create(\"button\", {\n        class: classes.page,\n        type: \"button\"\n      }, li);\n      var controls = Slides.getIn(i).map(function (Slide) {\n        return Slide.slide.id;\n      });\n      var text = !hasFocus() && perPage > 1 ? i18n.pageX : i18n.slideX;\n      bind(button, \"click\", apply(onClick, i));\n\n      if (options.paginationKeyboard) {\n        bind(button, \"keydown\", apply(onKeydown, i));\n      }\n\n      setAttribute(li, ROLE, \"presentation\");\n      setAttribute(button, ROLE, \"tab\");\n      setAttribute(button, ARIA_CONTROLS, controls.join(\" \"));\n      setAttribute(button, ARIA_LABEL, format(text, i + 1));\n      setAttribute(button, TAB_INDEX, -1);\n      items.push({\n        li: li,\n        button: button,\n        page: i\n      });\n    }\n  }\n\n  function onClick(page) {\n    go(\">\" + page, true);\n  }\n\n  function onKeydown(page, e) {\n    var length = items.length;\n    var key = normalizeKey(e);\n    var dir = getDirection();\n    var nextPage = -1;\n\n    if (key === resolve(ARROW_RIGHT, false, dir)) {\n      nextPage = ++page % length;\n    } else if (key === resolve(ARROW_LEFT, false, dir)) {\n      nextPage = (--page + length) % length;\n    } else if (key === \"Home\") {\n      nextPage = 0;\n    } else if (key === \"End\") {\n      nextPage = length - 1;\n    }\n\n    var item = items[nextPage];\n\n    if (item) {\n      focus(item.button);\n      go(\">\" + nextPage);\n      prevent(e, true);\n    }\n  }\n\n  function getDirection() {\n    return options.paginationDirection || options.direction;\n  }\n\n  function getAt(index) {\n    return items[Controller.toPage(index)];\n  }\n\n  function update() {\n    var prev = getAt(getIndex(true));\n    var curr = getAt(getIndex());\n\n    if (prev) {\n      var button = prev.button;\n      removeClass(button, CLASS_ACTIVE);\n      removeAttribute(button, ARIA_SELECTED);\n      setAttribute(button, TAB_INDEX, -1);\n    }\n\n    if (curr) {\n      var _button = curr.button;\n      addClass(_button, CLASS_ACTIVE);\n      setAttribute(_button, ARIA_SELECTED, true);\n      setAttribute(_button, TAB_INDEX, \"\");\n    }\n\n    emit(EVENT_PAGINATION_UPDATED, {\n      list: list,\n      items: items\n    }, prev, curr);\n  }\n\n  return {\n    items: items,\n    mount: mount,\n    destroy: destroy,\n    getAt: getAt,\n    update: update\n  };\n}\n\nvar TRIGGER_KEYS = [\" \", \"Enter\"];\n\nfunction Sync(Splide2, Components2, options) {\n  var isNavigation = options.isNavigation,\n      slideFocus = options.slideFocus;\n  var events = [];\n\n  function mount() {\n    Splide2.splides.forEach(function (target) {\n      if (!target.isParent) {\n        sync(Splide2, target.splide);\n        sync(target.splide, Splide2);\n      }\n    });\n\n    if (isNavigation) {\n      navigate();\n    }\n  }\n\n  function destroy() {\n    events.forEach(function (event) {\n      event.destroy();\n    });\n    empty(events);\n  }\n\n  function remount() {\n    destroy();\n    mount();\n  }\n\n  function sync(splide, target) {\n    var event = EventInterface(splide);\n    event.on(EVENT_MOVE, function (index, prev, dest) {\n      target.go(target.is(LOOP) ? dest : index);\n    });\n    events.push(event);\n  }\n\n  function navigate() {\n    var event = EventInterface(Splide2);\n    var on = event.on;\n    on(EVENT_CLICK, onClick);\n    on(EVENT_SLIDE_KEYDOWN, onKeydown);\n    on([EVENT_MOUNTED, EVENT_UPDATED], update);\n    events.push(event);\n    event.emit(EVENT_NAVIGATION_MOUNTED, Splide2.splides);\n  }\n\n  function update() {\n    setAttribute(Components2.Elements.list, ARIA_ORIENTATION, options.direction === TTB ? \"vertical\" : \"\");\n  }\n\n  function onClick(Slide) {\n    Splide2.go(Slide.index);\n  }\n\n  function onKeydown(Slide, e) {\n    if (includes(TRIGGER_KEYS, normalizeKey(e))) {\n      onClick(Slide);\n      prevent(e);\n    }\n  }\n\n  return {\n    setup: apply(Components2.Media.set, {\n      slideFocus: isUndefined(slideFocus) ? isNavigation : slideFocus\n    }, true),\n    mount: mount,\n    destroy: destroy,\n    remount: remount\n  };\n}\n\nfunction Wheel(Splide2, Components2, options) {\n  var _EventInterface12 = EventInterface(Splide2),\n      bind = _EventInterface12.bind;\n\n  var lastTime = 0;\n\n  function mount() {\n    if (options.wheel) {\n      bind(Components2.Elements.track, \"wheel\", onWheel, SCROLL_LISTENER_OPTIONS);\n    }\n  }\n\n  function onWheel(e) {\n    if (e.cancelable) {\n      var deltaY = e.deltaY;\n      var backwards = deltaY < 0;\n      var timeStamp = timeOf(e);\n\n      var _min = options.wheelMinThreshold || 0;\n\n      var sleep = options.wheelSleep || 0;\n\n      if (abs(deltaY) > _min && timeStamp - lastTime > sleep) {\n        Splide2.go(backwards ? \"<\" : \">\");\n        lastTime = timeStamp;\n      }\n\n      shouldPrevent(backwards) && prevent(e);\n    }\n  }\n\n  function shouldPrevent(backwards) {\n    return !options.releaseWheel || Splide2.state.is(MOVING) || Components2.Controller.getAdjacent(backwards) !== -1;\n  }\n\n  return {\n    mount: mount\n  };\n}\n\nvar SR_REMOVAL_DELAY = 90;\n\nfunction Live(Splide2, Components2, options) {\n  var _EventInterface13 = EventInterface(Splide2),\n      on = _EventInterface13.on;\n\n  var track = Components2.Elements.track;\n  var enabled = options.live && !options.isNavigation;\n  var sr = create(\"span\", CLASS_SR);\n  var interval = RequestInterval(SR_REMOVAL_DELAY, apply(toggle, false));\n\n  function mount() {\n    if (enabled) {\n      disable(!Components2.Autoplay.isPaused());\n      setAttribute(track, ARIA_ATOMIC, true);\n      sr.textContent = \"\\u2026\";\n      on(EVENT_AUTOPLAY_PLAY, apply(disable, true));\n      on(EVENT_AUTOPLAY_PAUSE, apply(disable, false));\n      on([EVENT_MOVED, EVENT_SCROLLED], apply(toggle, true));\n    }\n  }\n\n  function toggle(active) {\n    setAttribute(track, ARIA_BUSY, active);\n\n    if (active) {\n      append(track, sr);\n      interval.start();\n    } else {\n      remove(sr);\n      interval.cancel();\n    }\n  }\n\n  function destroy() {\n    removeAttribute(track, [ARIA_LIVE, ARIA_ATOMIC, ARIA_BUSY]);\n    remove(sr);\n  }\n\n  function disable(disabled) {\n    if (enabled) {\n      setAttribute(track, ARIA_LIVE, disabled ? \"off\" : \"polite\");\n    }\n  }\n\n  return {\n    mount: mount,\n    disable: disable,\n    destroy: destroy\n  };\n}\n\nvar ComponentConstructors = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  Media: Media,\n  Direction: Direction,\n  Elements: Elements,\n  Slides: Slides,\n  Layout: Layout,\n  Clones: Clones,\n  Move: Move,\n  Controller: Controller,\n  Arrows: Arrows,\n  Autoplay: Autoplay,\n  Cover: Cover,\n  Scroll: Scroll,\n  Drag: Drag,\n  Keyboard: Keyboard,\n  LazyLoad: LazyLoad,\n  Pagination: Pagination,\n  Sync: Sync,\n  Wheel: Wheel,\n  Live: Live\n});\nvar I18N = {\n  prev: \"Previous slide\",\n  next: \"Next slide\",\n  first: \"Go to first slide\",\n  last: \"Go to last slide\",\n  slideX: \"Go to slide %s\",\n  pageX: \"Go to page %s\",\n  play: \"Start autoplay\",\n  pause: \"Pause autoplay\",\n  carousel: \"carousel\",\n  slide: \"slide\",\n  select: \"Select a slide to show\",\n  slideLabel: \"%s of %s\"\n};\nvar DEFAULTS = {\n  type: \"slide\",\n  role: \"region\",\n  speed: 400,\n  perPage: 1,\n  cloneStatus: true,\n  arrows: true,\n  pagination: true,\n  paginationKeyboard: true,\n  interval: 5e3,\n  pauseOnHover: true,\n  pauseOnFocus: true,\n  resetProgress: true,\n  easing: \"cubic-bezier(0.25, 1, 0.5, 1)\",\n  drag: true,\n  direction: \"ltr\",\n  trimSpace: true,\n  focusableNodes: \"a, button, textarea, input, select, iframe\",\n  live: true,\n  classes: CLASSES,\n  i18n: I18N,\n  reducedMotion: {\n    speed: 0,\n    rewindSpeed: 0,\n    autoplay: \"pause\"\n  }\n};\n\nfunction Fade(Splide2, Components2, options) {\n  var Slides = Components2.Slides;\n\n  function mount() {\n    EventInterface(Splide2).on([EVENT_MOUNTED, EVENT_REFRESH], init);\n  }\n\n  function init() {\n    Slides.forEach(function (Slide) {\n      Slide.style(\"transform\", \"translateX(-\" + 100 * Slide.index + \"%)\");\n    });\n  }\n\n  function start(index, done) {\n    Slides.style(\"transition\", \"opacity \" + options.speed + \"ms \" + options.easing);\n    nextTick(done);\n  }\n\n  return {\n    mount: mount,\n    start: start,\n    cancel: noop\n  };\n}\n\nfunction Slide(Splide2, Components2, options) {\n  var Move = Components2.Move,\n      Controller = Components2.Controller,\n      Scroll = Components2.Scroll;\n  var list = Components2.Elements.list;\n  var transition = apply(style, list, \"transition\");\n  var endCallback;\n\n  function mount() {\n    EventInterface(Splide2).bind(list, \"transitionend\", function (e) {\n      if (e.target === list && endCallback) {\n        cancel();\n        endCallback();\n      }\n    });\n  }\n\n  function start(index, done) {\n    var destination = Move.toPosition(index, true);\n    var position = Move.getPosition();\n    var speed = getSpeed(index);\n\n    if (abs(destination - position) >= 1 && speed >= 1) {\n      if (options.useScroll) {\n        Scroll.scroll(destination, speed, false, done);\n      } else {\n        transition(\"transform \" + speed + \"ms \" + options.easing);\n        Move.translate(destination, true);\n        endCallback = done;\n      }\n    } else {\n      Move.jump(index);\n      done();\n    }\n  }\n\n  function cancel() {\n    transition(\"\");\n    Scroll.cancel();\n  }\n\n  function getSpeed(index) {\n    var rewindSpeed = options.rewindSpeed;\n\n    if (Splide2.is(SLIDE) && rewindSpeed) {\n      var prev = Controller.getIndex(true);\n      var end = Controller.getEnd();\n\n      if (prev === 0 && index >= end || prev >= end && index === 0) {\n        return rewindSpeed;\n      }\n    }\n\n    return options.speed;\n  }\n\n  return {\n    mount: mount,\n    start: start,\n    cancel: cancel\n  };\n}\n\nvar _Splide = /*#__PURE__*/function () {\n  function _Splide(target, options) {\n    this.event = EventInterface();\n    this.Components = {};\n    this.state = State(CREATED);\n    this.splides = [];\n    this._o = {};\n    this._E = {};\n    var root = isString(target) ? query(document, target) : target;\n    assert(root, root + \" is invalid.\");\n    this.root = root;\n    options = merge({\n      label: getAttribute(root, ARIA_LABEL) || \"\",\n      labelledby: getAttribute(root, ARIA_LABELLEDBY) || \"\"\n    }, DEFAULTS, _Splide.defaults, options || {});\n\n    try {\n      merge(options, JSON.parse(getAttribute(root, DATA_ATTRIBUTE)));\n    } catch (e) {\n      assert(false, \"Invalid JSON\");\n    }\n\n    this._o = Object.create(merge({}, options));\n  }\n\n  var _proto = _Splide.prototype;\n\n  _proto.mount = function mount(Extensions, Transition) {\n    var _this = this;\n\n    var state = this.state,\n        Components2 = this.Components;\n    assert(state.is([CREATED, DESTROYED]), \"Already mounted!\");\n    state.set(CREATED);\n    this._C = Components2;\n    this._T = Transition || this._T || (this.is(FADE) ? Fade : Slide);\n    this._E = Extensions || this._E;\n    var Constructors = assign({}, ComponentConstructors, this._E, {\n      Transition: this._T\n    });\n    forOwn(Constructors, function (Component, key) {\n      var component = Component(_this, Components2, _this._o);\n      Components2[key] = component;\n      component.setup && component.setup();\n    });\n    forOwn(Components2, function (component) {\n      component.mount && component.mount();\n    });\n    this.emit(EVENT_MOUNTED);\n    addClass(this.root, CLASS_INITIALIZED);\n    state.set(IDLE);\n    this.emit(EVENT_READY);\n    return this;\n  };\n\n  _proto.sync = function sync(splide) {\n    this.splides.push({\n      splide: splide\n    });\n    splide.splides.push({\n      splide: this,\n      isParent: true\n    });\n\n    if (this.state.is(IDLE)) {\n      this._C.Sync.remount();\n\n      splide.Components.Sync.remount();\n    }\n\n    return this;\n  };\n\n  _proto.go = function go(control) {\n    this._C.Controller.go(control);\n\n    return this;\n  };\n\n  _proto.on = function on(events, callback) {\n    this.event.on(events, callback);\n    return this;\n  };\n\n  _proto.off = function off(events) {\n    this.event.off(events);\n    return this;\n  };\n\n  _proto.emit = function emit(event) {\n    var _this$event;\n\n    (_this$event = this.event).emit.apply(_this$event, [event].concat(slice(arguments, 1)));\n\n    return this;\n  };\n\n  _proto.add = function add(slides, index) {\n    this._C.Slides.add(slides, index);\n\n    return this;\n  };\n\n  _proto.remove = function remove(matcher) {\n    this._C.Slides.remove(matcher);\n\n    return this;\n  };\n\n  _proto.is = function is(type) {\n    return this._o.type === type;\n  };\n\n  _proto.refresh = function refresh() {\n    this.emit(EVENT_REFRESH);\n    return this;\n  };\n\n  _proto.destroy = function destroy(completely) {\n    if (completely === void 0) {\n      completely = true;\n    }\n\n    var event = this.event,\n        state = this.state;\n\n    if (state.is(CREATED)) {\n      EventInterface(this).on(EVENT_READY, this.destroy.bind(this, completely));\n    } else {\n      forOwn(this._C, function (component) {\n        component.destroy && component.destroy(completely);\n      }, true);\n      event.emit(EVENT_DESTROY);\n      event.destroy();\n      completely && empty(this.splides);\n      state.set(DESTROYED);\n    }\n\n    return this;\n  };\n\n  _createClass(_Splide, [{\n    key: \"options\",\n    get: function get() {\n      return this._o;\n    },\n    set: function set(options) {\n      this._C.Media.set(options, true, true);\n    }\n  }, {\n    key: \"length\",\n    get: function get() {\n      return this._C.Slides.getLength(true);\n    }\n  }, {\n    key: \"index\",\n    get: function get() {\n      return this._C.Controller.getIndex();\n    }\n  }]);\n\n  return _Splide;\n}();\n\nvar Splide = _Splide;\nSplide.defaults = {};\nSplide.STATES = STATES;\nvar CLASS_RENDERED = \"is-rendered\";\nvar RENDERER_DEFAULT_CONFIG = {\n  listTag: \"ul\",\n  slideTag: \"li\"\n};\n\nvar Style = /*#__PURE__*/function () {\n  function Style(id, options) {\n    this.styles = {};\n    this.id = id;\n    this.options = options;\n  }\n\n  var _proto2 = Style.prototype;\n\n  _proto2.rule = function rule(selector, prop, value, breakpoint) {\n    breakpoint = breakpoint || \"default\";\n    var selectors = this.styles[breakpoint] = this.styles[breakpoint] || {};\n    var styles = selectors[selector] = selectors[selector] || {};\n    styles[prop] = value;\n  };\n\n  _proto2.build = function build() {\n    var _this2 = this;\n\n    var css = \"\";\n\n    if (this.styles.default) {\n      css += this.buildSelectors(this.styles.default);\n    }\n\n    Object.keys(this.styles).sort(function (n, m) {\n      return _this2.options.mediaQuery === \"min\" ? +n - +m : +m - +n;\n    }).forEach(function (breakpoint) {\n      if (breakpoint !== \"default\") {\n        css += \"@media screen and (max-width: \" + breakpoint + \"px) {\";\n        css += _this2.buildSelectors(_this2.styles[breakpoint]);\n        css += \"}\";\n      }\n    });\n    return css;\n  };\n\n  _proto2.buildSelectors = function buildSelectors(selectors) {\n    var _this3 = this;\n\n    var css = \"\";\n    forOwn(selectors, function (styles, selector) {\n      selector = (\"#\" + _this3.id + \" \" + selector).trim();\n      css += selector + \" {\";\n      forOwn(styles, function (value, prop) {\n        if (value || value === 0) {\n          css += prop + \": \" + value + \";\";\n        }\n      });\n      css += \"}\";\n    });\n    return css;\n  };\n\n  return Style;\n}();\n\nvar SplideRenderer = /*#__PURE__*/function () {\n  function SplideRenderer(contents, options, config, defaults) {\n    this.slides = [];\n    this.options = {};\n    this.breakpoints = [];\n    merge(DEFAULTS, defaults || {});\n    merge(merge(this.options, DEFAULTS), options || {});\n    this.contents = contents;\n    this.config = assign({}, RENDERER_DEFAULT_CONFIG, config || {});\n    this.id = this.config.id || uniqueId(\"splide\");\n    this.Style = new Style(this.id, this.options);\n    this.Direction = Direction(null, null, this.options);\n    assert(this.contents.length, \"Provide at least 1 content.\");\n    this.init();\n  }\n\n  SplideRenderer.clean = function clean(splide) {\n    var _EventInterface14 = EventInterface(splide),\n        on = _EventInterface14.on;\n\n    var root = splide.root;\n    var clones = queryAll(root, \".\" + CLASS_CLONE);\n    on(EVENT_MOUNTED, function () {\n      remove(child(root, \"style\"));\n    });\n    remove(clones);\n  };\n\n  var _proto3 = SplideRenderer.prototype;\n\n  _proto3.init = function init() {\n    this.parseBreakpoints();\n    this.initSlides();\n    this.registerRootStyles();\n    this.registerTrackStyles();\n    this.registerSlideStyles();\n    this.registerListStyles();\n  };\n\n  _proto3.initSlides = function initSlides() {\n    var _this4 = this;\n\n    push(this.slides, this.contents.map(function (content, index) {\n      content = isString(content) ? {\n        html: content\n      } : content;\n      content.styles = content.styles || {};\n      content.attrs = content.attrs || {};\n\n      _this4.cover(content);\n\n      var classes = _this4.options.classes.slide + \" \" + (index === 0 ? CLASS_ACTIVE : \"\");\n      assign(content.attrs, {\n        class: (classes + \" \" + (content.attrs.class || \"\")).trim(),\n        style: _this4.buildStyles(content.styles)\n      });\n      return content;\n    }));\n\n    if (this.isLoop()) {\n      this.generateClones(this.slides);\n    }\n  };\n\n  _proto3.registerRootStyles = function registerRootStyles() {\n    var _this5 = this;\n\n    this.breakpoints.forEach(function (_ref2) {\n      var width = _ref2[0],\n          options = _ref2[1];\n\n      _this5.Style.rule(\" \", \"max-width\", unit(options.width), width);\n    });\n  };\n\n  _proto3.registerTrackStyles = function registerTrackStyles() {\n    var _this6 = this;\n\n    var Style2 = this.Style;\n    var selector = \".\" + CLASS_TRACK;\n    this.breakpoints.forEach(function (_ref3) {\n      var width = _ref3[0],\n          options = _ref3[1];\n      Style2.rule(selector, _this6.resolve(\"paddingLeft\"), _this6.cssPadding(options, false), width);\n      Style2.rule(selector, _this6.resolve(\"paddingRight\"), _this6.cssPadding(options, true), width);\n      Style2.rule(selector, \"height\", _this6.cssTrackHeight(options), width);\n    });\n  };\n\n  _proto3.registerListStyles = function registerListStyles() {\n    var _this7 = this;\n\n    var Style2 = this.Style;\n    var selector = \".\" + CLASS_LIST;\n    this.breakpoints.forEach(function (_ref4) {\n      var width = _ref4[0],\n          options = _ref4[1];\n      Style2.rule(selector, \"transform\", _this7.buildTranslate(options), width);\n\n      if (!_this7.cssSlideHeight(options)) {\n        Style2.rule(selector, \"aspect-ratio\", _this7.cssAspectRatio(options), width);\n      }\n    });\n  };\n\n  _proto3.registerSlideStyles = function registerSlideStyles() {\n    var _this8 = this;\n\n    var Style2 = this.Style;\n    var selector = \".\" + CLASS_SLIDE;\n    this.breakpoints.forEach(function (_ref5) {\n      var width = _ref5[0],\n          options = _ref5[1];\n      Style2.rule(selector, \"width\", _this8.cssSlideWidth(options), width);\n      Style2.rule(selector, \"height\", _this8.cssSlideHeight(options) || \"100%\", width);\n      Style2.rule(selector, _this8.resolve(\"marginRight\"), unit(options.gap) || \"0px\", width);\n      Style2.rule(selector + \" > img\", \"display\", options.cover ? \"none\" : \"inline\", width);\n    });\n  };\n\n  _proto3.buildTranslate = function buildTranslate(options) {\n    var _this$Direction = this.Direction,\n        resolve = _this$Direction.resolve,\n        orient = _this$Direction.orient;\n    var values = [];\n    values.push(this.cssOffsetClones(options));\n    values.push(this.cssOffsetGaps(options));\n\n    if (this.isCenter(options)) {\n      values.push(this.buildCssValue(orient(-50), \"%\"));\n      values.push.apply(values, this.cssOffsetCenter(options));\n    }\n\n    return values.filter(Boolean).map(function (value) {\n      return \"translate\" + resolve(\"X\") + \"(\" + value + \")\";\n    }).join(\" \");\n  };\n\n  _proto3.cssOffsetClones = function cssOffsetClones(options) {\n    var _this$Direction2 = this.Direction,\n        resolve = _this$Direction2.resolve,\n        orient = _this$Direction2.orient;\n    var cloneCount = this.getCloneCount();\n\n    if (this.isFixedWidth(options)) {\n      var _this$parseCssValue = this.parseCssValue(options[resolve(\"fixedWidth\")]),\n          value = _this$parseCssValue.value,\n          unit2 = _this$parseCssValue.unit;\n\n      return this.buildCssValue(orient(value) * cloneCount, unit2);\n    }\n\n    var percent = 100 * cloneCount / options.perPage;\n    return orient(percent) + \"%\";\n  };\n\n  _proto3.cssOffsetCenter = function cssOffsetCenter(options) {\n    var _this$Direction3 = this.Direction,\n        resolve = _this$Direction3.resolve,\n        orient = _this$Direction3.orient;\n\n    if (this.isFixedWidth(options)) {\n      var _this$parseCssValue2 = this.parseCssValue(options[resolve(\"fixedWidth\")]),\n          value = _this$parseCssValue2.value,\n          unit2 = _this$parseCssValue2.unit;\n\n      return [this.buildCssValue(orient(value / 2), unit2)];\n    }\n\n    var values = [];\n    var perPage = options.perPage,\n        gap = options.gap;\n    values.push(orient(50 / perPage) + \"%\");\n\n    if (gap) {\n      var _this$parseCssValue3 = this.parseCssValue(gap),\n          _value = _this$parseCssValue3.value,\n          _unit = _this$parseCssValue3.unit;\n\n      var gapOffset = (_value / perPage - _value) / 2;\n      values.push(this.buildCssValue(orient(gapOffset), _unit));\n    }\n\n    return values;\n  };\n\n  _proto3.cssOffsetGaps = function cssOffsetGaps(options) {\n    var cloneCount = this.getCloneCount();\n\n    if (cloneCount && options.gap) {\n      var orient = this.Direction.orient;\n\n      var _this$parseCssValue4 = this.parseCssValue(options.gap),\n          value = _this$parseCssValue4.value,\n          unit2 = _this$parseCssValue4.unit;\n\n      if (this.isFixedWidth(options)) {\n        return this.buildCssValue(orient(value * cloneCount), unit2);\n      }\n\n      var perPage = options.perPage;\n      var gaps = cloneCount / perPage;\n      return this.buildCssValue(orient(gaps * value), unit2);\n    }\n\n    return \"\";\n  };\n\n  _proto3.resolve = function resolve(prop) {\n    return camelToKebab(this.Direction.resolve(prop));\n  };\n\n  _proto3.cssPadding = function cssPadding(options, right) {\n    var padding = options.padding;\n    var prop = this.Direction.resolve(right ? \"right\" : \"left\", true);\n    return padding && unit(padding[prop] || (isObject(padding) ? 0 : padding)) || \"0px\";\n  };\n\n  _proto3.cssTrackHeight = function cssTrackHeight(options) {\n    var height = \"\";\n\n    if (this.isVertical()) {\n      height = this.cssHeight(options);\n      assert(height, '\"height\" is missing.');\n      height = \"calc(\" + height + \" - \" + this.cssPadding(options, false) + \" - \" + this.cssPadding(options, true) + \")\";\n    }\n\n    return height;\n  };\n\n  _proto3.cssHeight = function cssHeight(options) {\n    return unit(options.height);\n  };\n\n  _proto3.cssSlideWidth = function cssSlideWidth(options) {\n    return options.autoWidth ? \"\" : unit(options.fixedWidth) || (this.isVertical() ? \"\" : this.cssSlideSize(options));\n  };\n\n  _proto3.cssSlideHeight = function cssSlideHeight(options) {\n    return unit(options.fixedHeight) || (this.isVertical() ? options.autoHeight ? \"\" : this.cssSlideSize(options) : this.cssHeight(options));\n  };\n\n  _proto3.cssSlideSize = function cssSlideSize(options) {\n    var gap = unit(options.gap);\n    return \"calc((100%\" + (gap && \" + \" + gap) + \")/\" + (options.perPage || 1) + (gap && \" - \" + gap) + \")\";\n  };\n\n  _proto3.cssAspectRatio = function cssAspectRatio(options) {\n    var heightRatio = options.heightRatio;\n    return heightRatio ? \"\" + 1 / heightRatio : \"\";\n  };\n\n  _proto3.buildCssValue = function buildCssValue(value, unit2) {\n    return \"\" + value + unit2;\n  };\n\n  _proto3.parseCssValue = function parseCssValue(value) {\n    if (isString(value)) {\n      var number = parseFloat(value) || 0;\n      var unit2 = value.replace(/\\d*(\\.\\d*)?/, \"\") || \"px\";\n      return {\n        value: number,\n        unit: unit2\n      };\n    }\n\n    return {\n      value: value,\n      unit: \"px\"\n    };\n  };\n\n  _proto3.parseBreakpoints = function parseBreakpoints() {\n    var _this9 = this;\n\n    var breakpoints = this.options.breakpoints;\n    this.breakpoints.push([\"default\", this.options]);\n\n    if (breakpoints) {\n      forOwn(breakpoints, function (options, width) {\n        _this9.breakpoints.push([width, merge(merge({}, _this9.options), options)]);\n      });\n    }\n  };\n\n  _proto3.isFixedWidth = function isFixedWidth(options) {\n    return !!options[this.Direction.resolve(\"fixedWidth\")];\n  };\n\n  _proto3.isLoop = function isLoop() {\n    return this.options.type === LOOP;\n  };\n\n  _proto3.isCenter = function isCenter(options) {\n    if (options.focus === \"center\") {\n      if (this.isLoop()) {\n        return true;\n      }\n\n      if (this.options.type === SLIDE) {\n        return !this.options.trimSpace;\n      }\n    }\n\n    return false;\n  };\n\n  _proto3.isVertical = function isVertical() {\n    return this.options.direction === TTB;\n  };\n\n  _proto3.buildClasses = function buildClasses() {\n    var options = this.options;\n    return [CLASS_ROOT, CLASS_ROOT + \"--\" + options.type, CLASS_ROOT + \"--\" + options.direction, options.drag && CLASS_ROOT + \"--draggable\", options.isNavigation && CLASS_ROOT + \"--nav\", CLASS_ACTIVE, !this.config.hidden && CLASS_RENDERED].filter(Boolean).join(\" \");\n  };\n\n  _proto3.buildAttrs = function buildAttrs(attrs) {\n    var attr = \"\";\n    forOwn(attrs, function (value, key) {\n      attr += value ? \" \" + camelToKebab(key) + \"=\\\"\" + value + \"\\\"\" : \"\";\n    });\n    return attr.trim();\n  };\n\n  _proto3.buildStyles = function buildStyles(styles) {\n    var style = \"\";\n    forOwn(styles, function (value, key) {\n      style += \" \" + camelToKebab(key) + \":\" + value + \";\";\n    });\n    return style.trim();\n  };\n\n  _proto3.renderSlides = function renderSlides() {\n    var _this10 = this;\n\n    var tag = this.config.slideTag;\n    return this.slides.map(function (content) {\n      return \"<\" + tag + \" \" + _this10.buildAttrs(content.attrs) + \">\" + (content.html || \"\") + \"</\" + tag + \">\";\n    }).join(\"\");\n  };\n\n  _proto3.cover = function cover(content) {\n    var styles = content.styles,\n        _content$html = content.html,\n        html = _content$html === void 0 ? \"\" : _content$html;\n\n    if (this.options.cover && !this.options.lazyLoad) {\n      var src = html.match(/<img.*?src\\s*=\\s*(['\"])(.+?)\\1.*?>/);\n\n      if (src && src[2]) {\n        styles.background = \"center/cover no-repeat url('\" + src[2] + \"')\";\n      }\n    }\n  };\n\n  _proto3.generateClones = function generateClones(contents) {\n    var classes = this.options.classes;\n    var count = this.getCloneCount();\n    var slides = contents.slice();\n\n    while (slides.length < count) {\n      push(slides, slides);\n    }\n\n    push(slides.slice(-count).reverse(), slides.slice(0, count)).forEach(function (content, index) {\n      var attrs = assign({}, content.attrs, {\n        class: content.attrs.class + \" \" + classes.clone\n      });\n      var clone = assign({}, content, {\n        attrs: attrs\n      });\n      index < count ? contents.unshift(clone) : contents.push(clone);\n    });\n  };\n\n  _proto3.getCloneCount = function getCloneCount() {\n    if (this.isLoop()) {\n      var options = this.options;\n\n      if (options.clones) {\n        return options.clones;\n      }\n\n      var perPage = max.apply(void 0, this.breakpoints.map(function (_ref6) {\n        var options2 = _ref6[1];\n        return options2.perPage;\n      }));\n      return perPage * ((options.flickMaxPages || 1) + 1);\n    }\n\n    return 0;\n  };\n\n  _proto3.renderArrows = function renderArrows() {\n    var html = \"\";\n    html += \"<div class=\\\"\" + this.options.classes.arrows + \"\\\">\";\n    html += this.renderArrow(true);\n    html += this.renderArrow(false);\n    html += \"</div>\";\n    return html;\n  };\n\n  _proto3.renderArrow = function renderArrow(prev) {\n    var _this$options = this.options,\n        classes = _this$options.classes,\n        i18n = _this$options.i18n;\n    var attrs = {\n      class: classes.arrow + \" \" + (prev ? classes.prev : classes.next),\n      type: \"button\",\n      ariaLabel: prev ? i18n.prev : i18n.next\n    };\n    return \"<button \" + this.buildAttrs(attrs) + \"><svg xmlns=\\\"\" + XML_NAME_SPACE + \"\\\" viewBox=\\\"0 0 \" + SIZE + \" \" + SIZE + \"\\\" width=\\\"\" + SIZE + \"\\\" height=\\\"\" + SIZE + \"\\\"><path d=\\\"\" + (this.options.arrowPath || PATH) + \"\\\" /></svg></button>\";\n  };\n\n  _proto3.html = function html() {\n    var _this$config = this.config,\n        rootClass = _this$config.rootClass,\n        listTag = _this$config.listTag,\n        arrows = _this$config.arrows,\n        beforeTrack = _this$config.beforeTrack,\n        afterTrack = _this$config.afterTrack,\n        slider = _this$config.slider,\n        beforeSlider = _this$config.beforeSlider,\n        afterSlider = _this$config.afterSlider;\n    var html = \"\";\n    html += \"<div id=\\\"\" + this.id + \"\\\" class=\\\"\" + this.buildClasses() + \" \" + (rootClass || \"\") + \"\\\">\";\n    html += \"<style>\" + this.Style.build() + \"</style>\";\n\n    if (slider) {\n      html += beforeSlider || \"\";\n      html += \"<div class=\\\"splide__slider\\\">\";\n    }\n\n    html += beforeTrack || \"\";\n\n    if (arrows) {\n      html += this.renderArrows();\n    }\n\n    html += \"<div class=\\\"splide__track\\\">\";\n    html += \"<\" + listTag + \" class=\\\"splide__list\\\">\";\n    html += this.renderSlides();\n    html += \"</\" + listTag + \">\";\n    html += \"</div>\";\n    html += afterTrack || \"\";\n\n    if (slider) {\n      html += \"</div>\";\n      html += afterSlider || \"\";\n    }\n\n    html += \"</div>\";\n    return html;\n  };\n\n  return SplideRenderer;\n}();\n\nexport { CLASSES, CLASS_ACTIVE, CLASS_ARROW, CLASS_ARROWS, CLASS_ARROW_NEXT, CLASS_ARROW_PREV, CLASS_CLONE, CLASS_CONTAINER, CLASS_FOCUS_IN, CLASS_INITIALIZED, CLASS_LIST, CLASS_LOADING, CLASS_NEXT, CLASS_OVERFLOW, CLASS_PAGINATION, CLASS_PAGINATION_PAGE, CLASS_PREV, CLASS_PROGRESS, CLASS_PROGRESS_BAR, CLASS_ROOT, CLASS_SLIDE, CLASS_SPINNER, CLASS_SR, CLASS_TOGGLE, CLASS_TOGGLE_PAUSE, CLASS_TOGGLE_PLAY, CLASS_TRACK, CLASS_VISIBLE, DEFAULTS, EVENT_ACTIVE, EVENT_ARROWS_MOUNTED, EVENT_ARROWS_UPDATED, EVENT_AUTOPLAY_PAUSE, EVENT_AUTOPLAY_PLAY, EVENT_AUTOPLAY_PLAYING, EVENT_CLICK, EVENT_DESTROY, EVENT_DRAG, EVENT_DRAGGED, EVENT_DRAGGING, EVENT_END_INDEX_CHANGED, EVENT_HIDDEN, EVENT_INACTIVE, EVENT_LAZYLOAD_LOADED, EVENT_MOUNTED, EVENT_MOVE, EVENT_MOVED, EVENT_NAVIGATION_MOUNTED, EVENT_OVERFLOW, EVENT_PAGINATION_MOUNTED, EVENT_PAGINATION_UPDATED, EVENT_READY, EVENT_REFRESH, EVENT_RESIZE, EVENT_RESIZED, EVENT_SCROLL, EVENT_SCROLLED, EVENT_SHIFTED, EVENT_SLIDE_KEYDOWN, EVENT_UPDATED, EVENT_VISIBLE, EventBinder, EventInterface, FADE, LOOP, LTR, RTL, RequestInterval, SLIDE, STATUS_CLASSES, Splide, SplideRenderer, State, TTB, Throttle, Splide as default };\n", "import type { DOMElementOptions, StylesObject } from '../../common/config/types';\n\n/**\n * DOM utility functions for safe DOM manipulation\n */\n\n/**\n * Safely query a single element\n */\nexport const querySelector = (selector: string): Element | null => {\n    try {\n        return document.querySelector(selector);\n    } catch {\n        return;\n    }\n};\n\n/**\n * Safely query multiple elements\n */\nexport const querySelectorAll = (selector: string): NodeListOf<Element> => {\n    try {\n        return document.querySelectorAll(selector);\n    } catch {\n        return document.querySelectorAll(''); // Returns empty NodeList\n    }\n};\n\n/**\n * Safely get element by ID\n */\nexport const getElementById = (id: string): HTMLElement | null => {\n    try {\n        return document.getElementById(id);\n    } catch {\n        return;\n    }\n};\n\n/**\n * Safely create element with options\n */\nexport const createElement = (\n    tagName: string,\n    options: DOMElementOptions = {},\n    innerHTML = ''\n): HTMLElement => {\n    try {\n        const element = document.createElement(tagName);\n\n        // Set attributes\n        for (const [key, value] of Object.entries(options)) {\n            if (key === 'className') {\n                element.className = String(value);\n            } else if (key === 'id') {\n                element.id = String(value);\n            } else {\n                element.setAttribute(key, String(value));\n            }\n        }\n\n        if (innerHTML) {\n            element.innerHTML = innerHTML;\n        }\n\n        return element;\n    } catch {\n        return document.createElement('div'); // Fallback\n    }\n};\n\n/**\n * Safely append child element\n */\nexport const appendChild = (parent: Element, child: Element): void => {\n    try {\n        parent.appendChild(child);\n    } catch {\n        // Silently handle append errors\n    }\n};\n\n/**\n * Safely prepend child element\n */\nexport const prependChild = (parent: Element, child: Element): void => {\n    try {\n        parent.prepend(child);\n    } catch {\n        // Silently handle prepend errors\n    }\n};\n\n/**\n * Safely remove element\n */\nexport const removeElement = (element: Element): void => {\n    try {\n        element.remove();\n    } catch {\n        // Silently handle removal errors\n    }\n};\n\n/**\n * Safely set styles on element\n */\nexport const setStyles = (element: HTMLElement, styles: StylesObject): void => {\n    try {\n        for (const [property, value] of Object.entries(styles)) {\n            element.style.setProperty(property, String(value));\n        }\n    } catch {\n        // Silently handle style errors\n    }\n};\n", "/**\n * Application constants for TagTiles extension\n */\n\n// Mobile detection constants\nexport const MO<PERSON>LE_DETECTION = {\n  USER_AGENT_SUBSTR_START: 0,\n  USER_AGENT_SUBSTR_LENGTH: 4,\n} as const;\n\n// Splide configuration constants\nexport const SPLIDE_CONFIG = {\n  MOBILE: {\n    GAP: '80px',\n    PER_PAGE: 4,\n  },\n  DESKTOP: {\n    GAP: '10px',\n    PER_PAGE: 7,\n  },\n  AUTOPLAY_INTERVAL: 3000,\n} as const;\n\n// Advanced splide configuration constants\nexport const ADVANCED_SPLIDE_CONFIG = {\n  MIN_SLIDES_FOR_LOOP: 2, // Splide requires minimum 2 slides for loop mode\n  MIN_SLIDES_FOR_AUTOPLAY: 2,\n  AUTOPLAY_INTERVAL: 3000,\n  TRANSITION_SPEED: 800,\n  GAP: '10px',\n  SPLIDE_INIT_DELAY: 100,\n} as const;\n\n// Error handling constants\nexport const ERROR_HANDLING = {\n  MAX_ERROR_LOG_ENTRIES: 50,\n  DOM_READY_TIMEOUT: 5000,\n} as const;\n\n// UI styling constants\nexport const UI_STYLES = {\n  SOCIAL_ICON_WIDTH: 32,\n  SOCIAL_ICON_MARGIN_LEFT: 20,\n  TAG_TEXT_FONT_SIZE: 14,\n  TAG_CONTAINER_PADDING_TOP: 10,\n  TAG_CONTAINER_MARGIN_TOP: 5,\n} as const;\n\n// Array and index constants\nexport const ARRAY_CONSTANTS = {\n  EMPTY_LENGTH: 0,\n  FIRST_INDEX: 0,\n  NOT_FOUND_INDEX: -1,\n  NEXT_ITEM_OFFSET: 1,\n  LAST_ITEM_OFFSET: -1,\n} as const;\n\n// Timing constants\nexport const TIMING = {\n  CHECK_INTERVAL: 10,\n  DATA_CHECK_INTERVAL: 100,\n} as const;\n\n// DOM element constants\nexport const DOM_ELEMENTS = {\n  SPLIDE_TAG_CONTAINER_ID: 'splideTagContainer',\n  SPLIDE_TAG_WRAPPER_ID: 'splideTagWrapper',\n} as const;\n\n// CSS class constants\nexport const CSS_CLASSES = {\n  SPLIDE: 'splide',\n  SPLIDE_TRACK: 'splide__track',\n  SPLIDE_LIST: 'splide__list',\n  SPLIDE_SLIDE: 'splide__slide',\n  SPLIDE_SLIDE_TAG: 'splide__slide-tag',\n  SPLIDE_SLIDE_TAG_INNER: 'splide__slide-tag-inner',\n  SPLIDE_SLIDE_TAG_INNER_MOBILE: 'splide__slide-tag-inner-mobile',\n  TAG_SPLIDE: 'tagSplide',\n  TAG_TILES: 'TagTiles',\n  TAG_TILE: 'TagTile',\n  TAG_TILE_NAME: 'TagTile-name',\n  TAG_TILE_DESCRIPTION: 'TagTile-description',\n  TAG_TEXT_OUTER_CONTAINER: 'TagTextOuterContainer',\n  TAG_TEXT_CONTAINER: 'TagTextContainer',\n  TAG_TEXT_ICON: 'TagTextIcon',\n} as const;\n\n// CSS selector constants\nexport const CSS_SELECTORS = {\n  TAGS_PAGE_CONTENT: '#content .container .TagsPage-content',\n  APP_CONTENT: '.App-content',\n} as const;\n\n// Extension configuration constants\nexport const EXTENSION_CONFIG = {\n  ID: 'wusong8899-tag-tiles',\n  TRANSLATION_PREFIX: 'wusong8899-tag-tiles',\n} as const;\n\n// Social media platform constants\nexport const SOCIAL_PLATFORMS = [\n  'Kick',\n  'Facebook',\n  'Twitter',\n  'YouTube',\n  'Instagram'\n] as const;\n\nexport type SocialPlatform = typeof SOCIAL_PLATFORMS[number];\n", "import { MO<PERSON>LE_DETECTION, SPLIDE_CONFIG } from '../../common/config/constants';\nimport type { MobileConfig } from '../../common/config/types';\n\n/**\n * Mobile detection utility functions\n */\n\n/**\n * Check if the current device is mobile\n */\nexport const isMobileDevice = (): boolean => {\n    try {\n        const { userAgent } = navigator;\n        const mobileIndicator = userAgent.substring(\n            MOBILE_DETECTION.USER_AGENT_SUBSTR_START,\n            MOBILE_DETECTION.USER_AGENT_SUBSTR_LENGTH\n        );\n        return mobileIndicator === 'Mobi';\n    } catch {\n        return false;\n    }\n};\n\n/**\n * Get splide configuration based on device type\n */\nexport const getSplideConfig = (): MobileConfig => {\n    if (isMobileDevice()) {\n        return {\n            gap: SPLIDE_CONFIG.MOBILE.GAP,\n            perPage: SPLIDE_CONFIG.MOBILE.PER_PAGE,\n        };\n    }\n\n    return {\n        gap: SPLIDE_CONFIG.DESKTOP.GAP,\n        perPage: SPLIDE_CONFIG.DESKTOP.PER_PAGE,\n    };\n};\n", "import type { RootConfig, Environment } from './types';\nimport {\n  EXTENSION_CONFIG,\n  TIMING,\n  DOM_ELEMENTS,\n  SPLIDE_CONFIG,\n  ADVANCED_SPLIDE_CONFIG\n} from './constants';\n\nexport const defaultConfig: RootConfig = {\n  env: (process.env.NODE_ENV as Environment) || 'production',\n  app: {\n    extensionId: EXTENSION_CONFIG.ID,\n    translationPrefix: EXTENSION_CONFIG.TRANSLATION_PREFIX,\n  },\n  tagTiles: {\n    autoplayInterval: SPLIDE_CONFIG.AUTOPLAY_INTERVAL,\n    checkInterval: TIMING.CHECK_INTERVAL,\n    dataCheckInterval: TIMING.DATA_CHECK_INTERVAL,\n    mobile: {\n      gap: SPLIDE_CONFIG.MOBILE.GAP,\n      perPage: SPLIDE_CONFIG.MOBILE.PER_PAGE,\n    },\n    desktop: {\n      gap: SPLIDE_CONFIG.DESKTOP.GAP,\n      perPage: SPLIDE_CONFIG.DESKTOP.PER_PAGE,\n    },\n    advanced: {\n      minSlidesForLoop: ADVANCED_SPLIDE_CONFIG.MIN_SLIDES_FOR_LOOP,\n      enableAutoplay: true,\n      autoplayInterval: ADVANCED_SPLIDE_CONFIG.AUTOPLAY_INTERVAL,\n      enableLoopMode: true,\n      transitionSpeed: ADVANCED_SPLIDE_CONFIG.TRANSITION_SPEED,\n      gap: ADVANCED_SPLIDE_CONFIG.GAP,\n      pauseOnMouseEnter: true,\n      enableGrabCursor: true,\n      enableFreeMode: false, // Splide doesn't have free mode like Swiper.js\n    },\n  },\n  ui: {\n    tagContainerId: DOM_ELEMENTS.SPLIDE_TAG_CONTAINER_ID,\n    tagWrapperId: DOM_ELEMENTS.SPLIDE_TAG_WRAPPER_ID,\n  },\n};\n", "import app from 'flarum/forum/app';\nimport { defaultConfig } from '../../common/config';\nimport type { AdvancedSplideConfig } from '../../common/config/types';\n\nconst EXTENSION_ID = 'wusong8899-tag-tiles';\n\n/**\n * Safely read a forum attribute if available\n */\nconst getForumAttribute = (key: string): unknown => {\n    try {\n        const forum = app && app.forum;\n        const attrFn = forum && forum.attribute;\n        if (typeof attrFn === 'function') {\n            return attrFn.call(forum, key);\n        }\n        return;\n    } catch {\n        return;\n    }\n};\n\n/**\n * Get a setting value with fallback to default\n */\nconst getSetting = <TValue>(settingKey: string, defaultValue: TValue): TValue => {\n    const value = getForumAttribute(`${EXTENSION_ID}.${settingKey}`);\n    const BOOLEAN_TRUE_VALUE = 1;\n\n    if (typeof value !== 'undefined' && value !== null) {\n        // Handle boolean conversion\n        if (typeof defaultValue === 'boolean') {\n            return (value === true || value === '1' || value === BOOLEAN_TRUE_VALUE) as TValue;\n        }\n        // Handle number conversion\n        if (typeof defaultValue === 'number') {\n            const numValue = Number(value);\n            if (Number.isNaN(numValue)) {\n                return defaultValue;\n            }\n            return numValue as TValue;\n        }\n        // Return as-is for other types\n        return value as TValue;\n    }\n    return defaultValue;\n};\n\n/**\n * Get advanced splide configuration from Flarum settings\n */\nexport const getAdvancedSplideConfig = (): AdvancedSplideConfig => {\n    const defaults = defaultConfig.tagTiles.advanced;\n\n    return {\n        minSlidesForLoop: getSetting('AdvancedSplideMinSlidesForLoop', defaults.minSlidesForLoop),\n        enableAutoplay: getSetting('AdvancedSplideEnableAutoplay', defaults.enableAutoplay),\n        autoplayInterval: getSetting('AdvancedSplideAutoplayInterval', defaults.autoplayInterval),\n        enableLoopMode: getSetting('AdvancedSplideEnableLoopMode', defaults.enableLoopMode),\n        transitionSpeed: getSetting('AdvancedSplideTransitionSpeed', defaults.transitionSpeed),\n        gap: getSetting('AdvancedSplideGap', defaults.gap),\n        pauseOnMouseEnter: getSetting('AdvancedSplidePauseOnMouseEnter', defaults.pauseOnMouseEnter),\n        enableGrabCursor: getSetting('AdvancedSplideEnableGrabCursor', defaults.enableGrabCursor),\n        enableFreeMode: getSetting('AdvancedSplideEnableFreeMode', defaults.enableFreeMode),\n    };\n};\n", "import { Splide } from '@splidejs/splide';\nimport app from 'flarum/forum/app';\nimport * as DOMUtils from '../utils/dom-utils';\nimport { isMobileDevice } from '../utils/mobile-detection';\nimport { ARRAY_CONSTANTS, ADVANCED_SPLIDE_CONFIG } from '../../common/config/constants';\nimport { defaultConfig } from '../../common/config';\nimport { getAdvancedSplideConfig } from '../utils/config-reader';\nimport type { TagData } from '../../common/config/types';\n\n/**\n * Tag Tiles Manager for converting TagTiles to swiper layout\n */\nexport class TagTilesManager {\n\n    /**\n     * Change category layout to swiper-based layout\n     */\n    changeCategoryLayout(): void {\n        try {\n            if (DOMUtils.getElementById(defaultConfig.ui.tagContainerId)) {\n                return; // Already exists\n            }\n\n            // Try immediate processing first\n            const tagTiles = DOMUtils.querySelectorAll(\".TagTile\");\n            if (tagTiles.length > ARRAY_CONSTANTS.EMPTY_LENGTH) {\n                this.processTagTiles(tagTiles);\n            } else {\n                // If no TagTiles found immediately, wait and retry\n                this.waitForTagTilesAndProcess();\n            }\n        } catch {\n            // Silently handle category layout errors\n        }\n    }\n\n    /**\n     * Wait for TagTiles to be available and process them\n     */\n    private waitForTagTilesAndProcess(): void {\n        const maxAttempts = 10;\n        const attemptInterval = 200;\n        let attempts = 0;\n\n        const checkAndProcess = (): void => {\n            attempts += ARRAY_CONSTANTS.NEXT_ITEM_OFFSET;\n            const tagTiles = DOMUtils.querySelectorAll(\".TagTile\");\n\n            if (tagTiles.length > ARRAY_CONSTANTS.EMPTY_LENGTH) {\n                // TagTiles found, process them\n                this.processTagTiles(tagTiles);\n            } else if (attempts < maxAttempts) {\n                // TagTiles not found yet, try again\n                setTimeout(checkAndProcess, attemptInterval);\n            }\n            // If max attempts reached and no TagTiles found, silently fail\n        };\n\n        checkAndProcess();\n    }\n\n    /**\n     * Process the found TagTiles\n     */\n    private processTagTiles(tagTiles: NodeListOf<Element>): void {\n        try {\n            const container = this.createTagSplideContainer();\n            if (!container) {\n                return;\n            }\n\n            const splide = this.createTagSplide(container);\n            if (!splide) {\n                return;\n            }\n\n            const wrapper = this.createTagSplideWrapper(splide);\n            if (!wrapper) {\n                return;\n            }\n\n            this.populateTagSlides(wrapper, tagTiles);\n            this.appendTagContainer(container);\n            this.addTagSplideContent(container);\n            this.removeOriginalTagTiles();\n            this.setupMobileStyles();\n            this.initializeTagSplide();\n\n            // Notify other extensions that the tags layout has changed\n            this.notifyTagsLayoutChanged();\n        } catch {\n            // Silently handle tag processing errors\n        }\n    }\n\n    /**\n     * Create tag splide container\n     */\n    private createTagSplideContainer(): HTMLElement {\n        const container = DOMUtils.createElement('div', {\n            className: 'splideTagContainer',\n            id: defaultConfig.ui.tagContainerId\n        });\n\n        const textContainer = DOMUtils.createElement('div', {\n            className: 'TagTextOuterContainer'\n        });\n\n        DOMUtils.appendChild(container, textContainer);\n        return container;\n    }\n\n    /**\n     * Create tag splide element\n     */\n    private createTagSplide(container: HTMLElement): HTMLElement {\n        const splide = DOMUtils.createElement('div', {\n            className: 'splide tagSplide'\n        });\n\n        // Append splide directly to the main container, not inside text container\n        DOMUtils.appendChild(container, splide);\n\n        return splide;\n    }\n\n    /**\n     * Create tag splide wrapper\n     */\n    private createTagSplideWrapper(splide: HTMLElement): HTMLElement {\n        const track = DOMUtils.createElement('div', {\n            className: 'splide__track'\n        });\n        DOMUtils.appendChild(splide, track);\n\n        const wrapper = DOMUtils.createElement('ul', {\n            className: 'splide__list',\n            id: defaultConfig.ui.tagWrapperId\n        });\n        DOMUtils.appendChild(track, wrapper);\n        return wrapper;\n    }\n\n    /**\n     * Populate tag slides\n     */\n    private populateTagSlides(wrapper: HTMLElement, tagTiles: NodeListOf<Element>): void {\n        const isMobile = isMobileDevice();\n\n        for (const tag of tagTiles) {\n            const tagElement = tag as HTMLElement;\n            const tagData = this.extractTagData(tagElement);\n\n            if (tagData) {\n                const slide = this.createTagSlide(tagData, isMobile);\n                DOMUtils.appendChild(wrapper, slide);\n            }\n        }\n    }\n\n    /**\n     * Extract tag data from DOM element\n     */\n    private extractTagData(tag: HTMLElement): TagData | void {\n        const linkElement = tag.querySelector('a') as HTMLAnchorElement;\n        const nameElement = tag.querySelector('.TagTile-name') as HTMLElement;\n        const descElement = tag.querySelector('.TagTile-description') as HTMLElement;\n\n        if (!linkElement || !nameElement) {\n            return;\n        }\n\n        // Get background from flarum-tag-background plugin or fallback to computed style\n        const backgroundImage = this.getTagBackgroundImage(linkElement.href, tag);\n        const computedStyle = globalThis.getComputedStyle(tag);\n        const background = backgroundImage || computedStyle.background;\n\n        let description = '';\n        let descColor = '';\n        if (descElement) {\n            description = descElement.textContent || '';\n            descColor = globalThis.getComputedStyle(descElement).color;\n        }\n\n        return {\n            url: linkElement.href,\n            background: background,\n            name: nameElement.textContent || '',\n            nameColor: globalThis.getComputedStyle(nameElement).color,\n            description,\n            descColor\n        };\n    }\n\n    /**\n     * Get tag background image from flarum-tag-background plugin\n     */\n    private getTagBackgroundImage(tagUrl: string, tagElement: HTMLElement): string | void {\n        try {\n            // Extract tag slug from URL\n            const url = new URL(tagUrl, globalThis.location.origin);\n            const parts = url.pathname.split('/').filter(Boolean);\n            const tIndex = parts.indexOf('t');\n            const tagsIndex = parts.indexOf('tags');\n\n            let slug = '';\n\n            if (tIndex !== ARRAY_CONSTANTS.NOT_FOUND_INDEX && parts[tIndex + ARRAY_CONSTANTS.NEXT_ITEM_OFFSET]) {\n                slug = parts[tIndex + ARRAY_CONSTANTS.NEXT_ITEM_OFFSET];\n            } else if (tagsIndex !== ARRAY_CONSTANTS.NOT_FOUND_INDEX && parts[tagsIndex + ARRAY_CONSTANTS.NEXT_ITEM_OFFSET]) {\n                slug = parts[tagsIndex + ARRAY_CONSTANTS.NEXT_ITEM_OFFSET];\n            } else if (parts.length > ARRAY_CONSTANTS.EMPTY_LENGTH) {\n                slug = parts[parts.length + ARRAY_CONSTANTS.LAST_ITEM_OFFSET]; // Get the last part of the URL\n            }\n\n            if (!slug) {\n                return;\n            }\n\n            // Get background URL using the same logic as flarum-tag-background\n            const bgUrl = this.getTagBackgroundUrlBySlug(slug);\n\n            if (bgUrl) {\n                return `url(${bgUrl})`;\n            }\n\n            return;\n        } catch {\n            // Fallback to checking inline styles set by flarum-tag-background\n            const inlineBackground = tagElement.style.background;\n            if (inlineBackground && inlineBackground.includes('url(')) {\n                return inlineBackground;\n            }\n            return;\n        }\n    }\n\n    /**\n     * Get tag background URL by slug - shared logic with flarum-tag-background\n     */\n    private getTagBackgroundUrlBySlug(slug: string): string | void {\n        try {\n            // Get tag from Flarum store\n            const tags = app.store.all('tags') as unknown[];\n            const tagModel = tags.find((tagItem: unknown) => {\n                const tagRecord = tagItem as Record<string, unknown>;\n                let tagSlug = '';\n\n                if (typeof tagRecord.slug === 'function') {\n                    tagSlug = tagRecord.slug();\n                } else if (tagRecord.attribute && typeof tagRecord.attribute === 'function') {\n                    tagSlug = tagRecord.attribute('slug');\n                }\n\n                return tagSlug === slug;\n            });\n\n            if (!tagModel) {\n                return;\n            }\n\n            // Get background URL from tag model\n            const tagRecord = tagModel as Record<string, unknown>;\n\n            if (tagRecord.attribute && typeof tagRecord.attribute === 'function') {\n                const bgUrl = tagRecord.attribute('wusong8899BackgroundURL');\n                if (bgUrl) {\n                    return bgUrl;\n                }\n            }\n\n            return;\n        } catch {\n            return;\n        }\n    }\n\n    /**\n     * Create individual tag slide\n     */\n    private createTagSlide(tagData: TagData, isMobile: boolean): HTMLElement {\n        const slide = DOMUtils.createElement('li', {\n            className: 'splide__slide splide__slide-tag'\n        });\n\n        let innerClass = 'splide__slide-tag-inner';\n        if (isMobile) {\n            innerClass = 'splide__slide-tag-inner-mobile';\n        }\n\n        const backgroundStyle = `background:${tagData.background};background-size: cover;background-position: center;background-repeat: no-repeat;`;\n\n        // Check if there's a background image (from flarum-tag-background plugin)\n        const hasBackgroundImage = this.hasBackgroundImage(tagData.background);\n\n        // If there's a background image, hide the text; otherwise show it\n        let textContent = '';\n        if (!hasBackgroundImage) {\n            textContent = `\n            <div style='font-weight:bold;font-size:14px;color:${tagData.nameColor}'>\n                ${tagData.name}\n            </div>\n        `;\n        }\n\n        slide.innerHTML = `\n            <a href='${tagData.url}'>\n                <div class='${innerClass}' style='${backgroundStyle}'>\n                    ${textContent}\n                </div>\n            </a>\n        `;\n\n        return slide;\n    }\n\n    /**\n     * Check if background contains an image URL\n     */\n    private hasBackgroundImage(background: string): boolean {\n        if (!background) {\n            return false;\n        }\n\n        // Check if background contains url() function\n        return background.includes('url(') && !background.includes('url()');\n    }\n\n    /**\n     * Append tag container to DOM\n     */\n    private appendTagContainer(container: HTMLElement): void {\n        const contentElement = DOMUtils.querySelector(\"#content .container .TagsPage-content\");\n        if (contentElement) {\n            DOMUtils.prependChild(contentElement, container);\n        }\n    }\n\n    /**\n     * Add additional content to tag container\n     */\n    private addTagSplideContent(container: HTMLElement): void {\n        const textContainer = container.querySelector('.TagTextOuterContainer');\n        if (textContainer) {\n            const titleElement = DOMUtils.createElement('div', {\n                className: 'TagTextContainer'\n            }, \"<div class='TagTextIcon'></div>中文玩家社区资讯\");\n\n            DOMUtils.prependChild(textContainer, titleElement);\n\n            const socialButtons = this.createSocialButtonsHTML();\n            textContainer.insertAdjacentHTML('beforeend', socialButtons);\n        }\n    }\n\n    /**\n     * Create social buttons HTML\n     */\n    private createSocialButtonsHTML(): string {\n        const { extensionId } = defaultConfig.app;\n\n        // Define social media platforms with their settings keys and default icons\n        const socialPlatforms = [\n            {\n                urlKey: `${extensionId}.SocialKickUrl`,\n                iconKey: `${extensionId}.SocialKickIcon`,\n                defaultIcon: ''\n            },\n            {\n                urlKey: `${extensionId}.SocialFacebookUrl`,\n                iconKey: `${extensionId}.SocialFacebookIcon`,\n                defaultIcon: ''\n            },\n            {\n                urlKey: `${extensionId}.SocialTwitterUrl`,\n                iconKey: `${extensionId}.SocialTwitterIcon`,\n                defaultIcon: ''\n            },\n            {\n                urlKey: `${extensionId}.SocialYouTubeUrl`,\n                iconKey: `${extensionId}.SocialYouTubeIcon`,\n                defaultIcon: ''\n            },\n            {\n                urlKey: `${extensionId}.SocialInstagramUrl`,\n                iconKey: `${extensionId}.SocialInstagramIcon`,\n                defaultIcon: ''\n            }\n        ];\n\n        // Generate social buttons HTML\n        const socialButtons = socialPlatforms\n            .map((platform, index) => {\n                const url = app.forum.attribute(platform.urlKey) || '';\n                const iconUrl = app.forum.attribute(platform.iconKey) || platform.defaultIcon;\n\n                // Only render button if both URL and icon are provided\n                if (!url.trim() || !iconUrl.trim()) {\n                    return '';\n                }\n\n                let marginStyle = '';\n                if (index > ARRAY_CONSTANTS.FIRST_INDEX) {\n                    marginStyle = 'margin-left: 20px;';\n                }\n                return `<img onClick=\"window.open('${url}', '_blank')\" style=\"width: 32px;${marginStyle}\" src=\"${iconUrl}\">`;\n            })\n            .filter(button => button !== '') // Remove empty buttons\n            .join('');\n\n        // Only render the container if there are social buttons\n        if (!socialButtons) {\n            return '';\n        }\n\n        return `\n            <div style=\"text-align:center;padding-top: 10px;\">\n                <button class=\"Button Button--primary\" type=\"button\" style=\"font-weight: normal !important; color:#ffa000; background: #1a1d2e !important;border-radius: 2rem !important;\">\n                    <div style=\"margin-top: 5px;\" class=\"Button-label\">\n                        ${socialButtons}\n                    </div>\n                </button>\n            </div>\n        `;\n    }\n\n    /**\n     * Remove original tag tiles\n     */\n    private removeOriginalTagTiles(): void {\n        const tagTiles = DOMUtils.querySelector(\".TagTiles\");\n        if (tagTiles) {\n            DOMUtils.removeElement(tagTiles);\n        }\n    }\n\n    /**\n     * Setup mobile-specific styles\n     */\n    private setupMobileStyles(): void {\n        if (isMobileDevice()) {\n            const app = DOMUtils.getElementById(\"app\");\n            const appContent = DOMUtils.querySelector(\".App-content\") as HTMLElement;\n\n            if (app) {\n                DOMUtils.setStyles(app, { 'overflow-x': 'hidden' });\n            }\n\n            if (appContent) {\n                DOMUtils.setStyles(appContent, {\n                    'min-height': 'auto',\n                    'background': ''\n                });\n            }\n        }\n    }\n\n    /**\n     * Initialize tag splide with advanced configuration\n     */\n    private initializeTagSplide(): void {\n        const advancedConfig = getAdvancedSplideConfig();\n\n        setTimeout(() => {\n            try {\n                // Check if we have enough slides for loop mode\n                const slides = document.querySelectorAll('.tagSplide .splide__slide');\n                const hasEnoughSlides = slides.length >= advancedConfig.minSlidesForLoop;\n\n                // Determine if we should enable loop mode\n                const shouldEnableLoop = advancedConfig.enableLoopMode && hasEnoughSlides;\n\n                // Configure autoplay - Enable autoplay if we have at least 2 slides and autoplay is enabled\n                let autoplayConfig: object | false = false;\n                const shouldEnableAutoplay = advancedConfig.enableAutoplay && slides.length >= ADVANCED_SPLIDE_CONFIG.MIN_SLIDES_FOR_AUTOPLAY;\n\n                if (shouldEnableAutoplay) {\n                    autoplayConfig = {\n                        interval: advancedConfig.autoplayInterval,\n                        pauseOnHover: advancedConfig.pauseOnMouseEnter,\n                        pauseOnFocus: true,\n                        rewind: false,\n                    };\n                }\n\n                // Configure slides per view based on device\n                const isMobile = isMobileDevice();\n                const MOBILE_PER_PAGE = 3;\n                let perPageValue = 'auto';\n                if (isMobile) {\n                    perPageValue = MOBILE_PER_PAGE;\n                }\n\n                // Determine slide type\n                let slideType: 'slide' | 'loop' = 'slide';\n                if (shouldEnableLoop) {\n                    slideType = 'loop';\n                }\n\n                const splideInstance = new Splide('.tagSplide', {\n                    perPage: perPageValue,\n                    gap: advancedConfig.gap,\n                    type: slideType,\n                    autoplay: autoplayConfig,\n                    speed: advancedConfig.transitionSpeed,\n                    drag: advancedConfig.enableGrabCursor,\n                    focus: ARRAY_CONSTANTS.EMPTY_LENGTH, // Start from the first slide\n                    pagination: false,\n                    arrows: true\n                });\n\n                splideInstance.mount();\n\n                // Store splide instance for potential cleanup\n                if (splideInstance) {\n                    // Splide initialized successfully\n                    // Debug: Log splide initialization\n                    if (process.env.NODE_ENV === 'development') {\n                        // Development logging would go here\n                    }\n                }\n            } catch {\n                // Silently handle Splide initialization errors\n            }\n        }, ADVANCED_SPLIDE_CONFIG.SPLIDE_INIT_DELAY);\n    }\n\n    /**\n     * Notify other extensions that the tags layout has changed\n     */\n    private notifyTagsLayoutChanged(): void {\n        try {\n            // Dispatch custom event to notify other extensions\n            const event = new CustomEvent('tagsLayoutChanged', {\n                detail: {\n                    extensionId: defaultConfig.app.extensionId,\n                    layoutType: 'splide'\n                }\n            });\n            document.dispatchEvent(event);\n        } catch {\n            // Silently handle event dispatch errors\n        }\n    }\n}\n", "import { ERROR_HANDLING } from '../../common/config/constants';\nimport type { ErrorLogEntry } from '../../common/config/types';\n\n/**\n * Error handling utility for the TagTiles extension\n */\nexport class ErrorHandler {\n    private static instance: <PERSON><PERSON>r<PERSON>andler;\n    private errorLog: ErrorLogEntry[] = [];\n    private isInitialized = false;\n\n    private constructor() {\n        // Private constructor for singleton pattern\n    }\n\n    /**\n     * Get singleton instance\n     */\n    public static getInstance(): ErrorHandler {\n        if (!ErrorHandler.instance) {\n            ErrorHandler.instance = new ErrorHandler();\n        }\n        return ErrorHandler.instance;\n    }\n\n    /**\n     * Initialize error handler\n     */\n    public initialize(): boolean {\n        try {\n            if (this.isInitialized) {\n                return true;\n            }\n\n            // Set up global error handling\n            this.setupGlobalErrorHandling();\n            this.isInitialized = true;\n            return true;\n        } catch {\n            return false;\n        }\n    }\n\n    /**\n     * Handle synchronous errors\n     */\n    public handleSync<TResult>(callback: () => TResult, context: string): TResult | void {\n        try {\n            return callback();\n        } catch (error) {\n            this.logError(error as Error, context);\n            return;\n        }\n    }\n\n    /**\n     * Handle asynchronous errors\n     */\n    public handleAsync<TResult>(callback: () => Promise<TResult>, context: string): Promise<TResult | void> {\n        return callback().catch((error) => {\n            this.logError(error as Error, context);\n            return;\n        });\n    }\n\n    /**\n     * Log error with context\n     */\n    private logError(error: Error, context: string): void {\n        try {\n            const entry: ErrorLogEntry = {\n                timestamp: new Date(),\n                error,\n                context,\n            };\n\n            this.errorLog.push(entry);\n\n            // Keep log size manageable\n            if (this.errorLog.length > ERROR_HANDLING.MAX_ERROR_LOG_ENTRIES) {\n                this.errorLog.shift();\n            }\n\n            // Log to console in development\n            if (process.env.NODE_ENV === 'development') {\n                // Development logging is handled elsewhere\n            }\n        } catch {\n            // Silently handle logging errors\n        }\n    }\n\n    /**\n     * Set up global error handling\n     */\n    private setupGlobalErrorHandling(): void {\n        try {\n            // Handle unhandled promise rejections\n            globalThis.addEventListener('unhandledrejection', (event) => {\n                this.logError(\n                    new Error(String(event.reason)),\n                    'Unhandled Promise Rejection'\n                );\n            });\n        } catch {\n            // Silently handle setup errors\n        }\n    }\n\n    /**\n     * Get error log (for debugging)\n     */\n    public getErrorLog(): ErrorLogEntry[] {\n        return [...this.errorLog];\n    }\n\n    /**\n     * Clear error log\n     */\n    public clearErrorLog(): void {\n        this.errorLog = [];\n    }\n}\n", "import app from 'flarum/forum/app';\nimport { defaultConfig } from '../../common/config';\n\n/**\n * Configuration manager for the TagTiles extension\n */\nexport class ConfigManager {\n    private static instance: ConfigManager;\n\n    private constructor() {\n        // Private constructor for singleton pattern\n    }\n\n    /**\n     * Get singleton instance\n     */\n    public static getInstance(): ConfigManager {\n        if (!ConfigManager.instance) {\n            ConfigManager.instance = new ConfigManager();\n        }\n        return ConfigManager.instance;\n    }\n\n    /**\n     * Check if current page is tags page\n     */\n    public isTagsPage(): boolean {\n        try {\n            const currentRoute = app.current.get('routeName');\n            return currentRoute === 'tags';\n        } catch {\n            // Fallback: check URL\n            try {\n                return globalThis.location.pathname.includes('/tags');\n            } catch {\n                return false;\n            }\n        }\n    }\n\n    /**\n     * Get extension configuration\n     */\n    public getConfig(): typeof defaultConfig {\n        return defaultConfig;\n    }\n\n    /**\n     * Check if extension is properly configured\n     */\n    public isConfigured(): boolean {\n        try {\n            // Check if at least one social media platform is configured\n            const socialPlatforms = ['Kick', 'Facebook', 'Twitter', 'YouTube', 'Instagram'];\n\n            for (const platform of socialPlatforms) {\n                const url = app.forum.attribute(`${defaultConfig.app.extensionId}.Social${platform}Url`);\n                const icon = app.forum.attribute(`${defaultConfig.app.extensionId}.Social${platform}Icon`);\n\n                if (url && icon) {\n                    return true;\n                }\n            }\n\n            return false;\n        } catch {\n            return false;\n        }\n    }\n}\n", "import { extend } from 'flarum/common/extend';\nimport app from 'flarum/forum/app';\nimport TagsPage from 'flarum/tags/components/TagsPage';\n\nimport { TagTilesManager } from './components/tag-tiles-manager';\nimport { <PERSON>rror<PERSON>andler } from './utils/error-handler';\nimport { ConfigManager } from './utils/config-manager';\nimport { defaultConfig } from '../common/config';\n\n/**\n * Main extension initializer for TagTiles\n */\napp.initializers.add(defaultConfig.app.extensionId, () => {\n    const errorHandler = ErrorHandler.getInstance();\n    const configManager = ConfigManager.getInstance();\n\n    // Initialize error handling\n    if (!errorHandler.initialize()) {\n        return;\n    }\n\n    const tagTilesManager = new TagTilesManager();\n\n    // Extend TagsPage to setup UI components when the page loads\n    extend(TagsPage.prototype, 'oncreate', function tagsPageOnCreateExtension(_vnode: unknown) {\n        errorHandler.handleSync(() => {\n            if (configManager.isTagsPage()) {\n                // Force UI components setup\n                const DOM_READY_DELAY = 100;\n                setTimeout(() => {\n                    tagTilesManager.changeCategoryLayout();\n                }, DOM_READY_DELAY);\n            }\n        }, 'TagsPage oncreate extension');\n    });\n\n    extend(TagsPage.prototype, 'onupdate', function tagsPageOnUpdateExtension(_vnode: unknown) {\n        errorHandler.handleSync(() => {\n            // Check if splide container doesn't exist and create it\n            if (!document.getElementById(defaultConfig.ui.tagContainerId)) {\n                const DOM_READY_DELAY = 100;\n                setTimeout(() => {\n                    tagTilesManager.changeCategoryLayout();\n                }, DOM_READY_DELAY);\n            }\n        }, 'TagsPage onupdate extension');\n    });\n});\n"], "names": ["_defineProperties", "target", "props", "i", "descriptor", "_createClass", "<PERSON><PERSON><PERSON><PERSON>", "protoProps", "staticProps", "MEDIA_PREFERS_REDUCED_MOTION", "CREATED", "MOUNTED", "IDLE", "MOVING", "SCROLLING", "DRAGGING", "DESTROYED", "STATES", "empty", "array", "slice", "arrayLike", "start", "end", "apply", "func", "nextTick", "noop", "raf", "typeOf", "type", "subject", "isObject", "isNull", "isArray", "isFunction", "isString", "isUndefined", "isHTMLElement", "toArray", "value", "for<PERSON>ach", "values", "iteratee", "includes", "push", "items", "toggleClass", "elm", "classes", "add", "name", "addClass", "append", "parent", "children", "before", "nodes", "ref", "node", "matches", "selector", "children2", "child", "ownKeys", "forOwn", "object", "right", "key", "assign", "source", "merge", "omit", "keys", "removeAttribute", "elms", "attrs", "attr", "setAttribute", "value2", "create", "tag", "style", "prop", "display", "display2", "focus", "getAttribute", "hasClass", "className", "rect", "remove", "parseHtml", "html", "prevent", "stopPropagation", "query", "queryAll", "removeClass", "timeOf", "unit", "PROJECT_CODE", "DATA_ATTRIBUTE", "assert", "condition", "message", "min", "max", "floor", "ceil", "abs", "approximatelyEqual", "x", "y", "epsilon", "between", "number", "exclusive", "minimum", "maximum", "clamp", "sign", "format", "string", "replacements", "replacement", "pad", "ids", "uniqueId", "prefix", "EventBinder", "listeners", "bind", "targets", "events", "callback", "options", "forEachEvent", "event", "namespace", "isEventTarget", "remover", "unbind", "listener", "dispatch", "detail", "e", "bubbles", "events2", "eventNS", "fragment", "destroy", "data", "EVENT_MOUNTED", "EVENT_READY", "EVENT_MOVE", "EVENT_MOVED", "EVENT_CLICK", "EVENT_ACTIVE", "EVENT_INACTIVE", "EVENT_VISIBLE", "EVENT_HIDDEN", "EVENT_REFRESH", "EVENT_UPDATED", "EVENT_RESIZE", "EVENT_RESIZED", "EVENT_DRAG", "EVENT_DRAGGING", "EVENT_DRAGGED", "EVENT_SCROLL", "EVENT_SCROLLED", "EVENT_OVERFLOW", "EVENT_DESTROY", "EVENT_ARROWS_MOUNTED", "EVENT_ARROWS_UPDATED", "EVENT_PAGINATION_MOUNTED", "EVENT_PAGINATION_UPDATED", "EVENT_NAVIGATION_MOUNTED", "EVENT_AUTOPLAY_PLAY", "EVENT_AUTOPLAY_PLAYING", "EVENT_AUTOPLAY_PAUSE", "EVENT_LAZYLOAD_LOADED", "EVENT_SLIDE_KEYDOWN", "EVENT_SHIFTED", "EVENT_END_INDEX_CHANGED", "EventInterface", "Splide2", "bus", "binder", "on", "emit", "RequestInterval", "interval", "onInterval", "onUpdate", "limit", "now", "startTime", "rate", "id", "paused", "count", "update", "pause", "resume", "cancel", "rewind", "set", "time", "isPaused", "State", "initialState", "state", "is", "states", "<PERSON>hrottle", "duration", "Media", "Components2", "breakpoints", "reducedMotion", "queries", "setup", "isMin", "n", "m", "register", "completely", "options2", "queryList", "destroyed", "direction", "merged", "merged2", "entry", "reduce", "enable", "opts", "base", "notify", "ARROW", "ARROW_LEFT", "ARROW_RIGHT", "ARROW_UP", "ARROW_DOWN", "RTL", "TTB", "ORIENTATION_MAP", "Direction", "resolve", "axisOnly", "index", "match", "offset", "orient", "ROLE", "TAB_INDEX", "DISABLED", "ARIA_PREFIX", "ARIA_CONTROLS", "ARIA_CURRENT", "ARIA_SELECTED", "ARIA_LABEL", "ARIA_LABELLEDBY", "ARIA_HIDDEN", "ARIA_ORIENTATION", "ARIA_ROLEDESCRIPTION", "ARIA_LIVE", "ARIA_BUSY", "ARIA_ATOMIC", "ALL_ATTRIBUTES", "CLASS_PREFIX", "STATUS_CLASS_PREFIX", "CLASS_ROOT", "CLASS_TRACK", "CLASS_LIST", "CLASS_SLIDE", "CLASS_CLONE", "CLASS_CONTAINER", "CLASS_ARROWS", "CLASS_ARROW", "CLASS_ARROW_PREV", "CLASS_ARROW_NEXT", "CLASS_PAGINATION", "CLASS_PAGINATION_PAGE", "CLASS_PROGRESS", "CLASS_PROGRESS_BAR", "CLASS_TOGGLE", "CLASS_SPINNER", "CLASS_SR", "CLASS_INITIALIZED", "CLASS_ACTIVE", "CLASS_PREV", "CLASS_NEXT", "CLASS_VISIBLE", "CLASS_LOADING", "CLASS_FOCUS_IN", "CLASS_OVERFLOW", "STATUS_CLASSES", "CLASSES", "closest", "from", "FRICTION", "LOG_INTERVAL", "POINTER_DOWN_EVENTS", "POINTER_MOVE_EVENTS", "POINTER_UP_EVENTS", "Elements", "_EventInterface", "root", "i18n", "elements", "slides", "rootClasses", "trackClasses", "track", "list", "isUsingKey", "collect", "init", "mount", "getClasses", "find", "role", "SLIDE", "LOOP", "FADE", "Slide$1", "slideIndex", "slide", "Components", "isNavigation", "updateOnMove", "pagination", "slideFocus", "styles", "label", "isClone", "container", "listen", "self", "initNavigation", "onMove", "controls", "Slide2", "curr", "updateActivity", "updateVisibility", "active", "isActive", "visible", "isVisible", "hidden", "style$1", "useContainer", "trackRect", "slideRect", "left", "<PERSON><PERSON><PERSON><PERSON>", "distance", "diff", "Slides", "_EventInterface2", "_Components2$Elements", "Slides2", "forEach$1", "Slide1", "get", "excludeClones", "filter", "getIn", "page", "Controller", "getAt", "observeImages", "remove$1", "matcher", "images", "length", "img", "<PERSON><PERSON><PERSON><PERSON>", "isEnough", "Layout", "_EventInterface3", "_Components2$Elements2", "styleSlides", "vertical", "rootRect", "overflow", "resize", "cssPadding", "force", "newRect", "cssTrackHeight", "cssSlideWidth", "cssSlideHeight", "isOverflow", "padding", "height", "cssHeight", "cssSlideSize", "gap", "listSize", "slideSize", "withoutGap", "Slide", "getGap", "totalSize", "sliderSize", "getPadding", "MULTIPLIER", "<PERSON><PERSON><PERSON>", "clones", "cloneCount", "remount", "observe", "computeCloneCount", "generate", "isHead", "clone", "cloneDeep", "clones2", "fixedSize", "fixedCount", "Move", "_EventInterface4", "_Components2$Layout", "_Components2$Directio", "_Components2$Elements3", "Transition", "reposition", "jump", "move", "dest", "prev", "canShift", "translate", "shift", "getPosition", "toPosition", "position", "preventLoop", "destination", "loop", "toIndex", "exceededMax", "exceededMin", "backwards", "excess", "getLimit", "size", "minDistance", "trimming", "trim", "shifted", "exceededLimit", "_EventInterface5", "_Components2$Slides", "omitEnd", "isLoop", "isSlide", "getNext", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "getPrev", "currIndex", "endIndex", "prevIndex", "slideCount", "perMove", "perPage", "onResized", "getEnd", "go", "control", "allowSameIndex", "isBusy", "parse", "setIndex", "scroll", "snap", "_ref", "indicator", "computeDestIndex", "hasFocus", "snapPage", "computeMovableDestIndex", "toPage", "toDest", "getIndex", "XML_NAME_SPACE", "PATH", "SIZE", "Arrows", "placeholder", "wrapper", "next", "created", "wrapperClasses", "arrows", "enabled", "createArrows", "createArrow", "prev2", "arrow", "nextIndex", "prevLabel", "next<PERSON><PERSON><PERSON>", "INTERVAL_DATA_ATTRIBUTE", "Autoplay", "_EventInterface6", "onAnimationFrame", "_Components2$Elements4", "toggle", "autoplay", "hovered", "focused", "stopped", "play", "autoToggle", "stop", "bar", "Cover", "_EventInterface7", "cover", "cover2", "BOUNCE_DIFF_THRESHOLD", "BOUNCE_DURATION", "FRICTION_FACTOR", "BASE_VELOCITY", "MIN_DURATION", "<PERSON><PERSON>", "_EventInterface8", "friction", "clear", "onScrolled", "noConstrain", "noDistance", "onEnd", "to", "easing", "t", "easingFunc", "SCROLL_LISTENER_OPTIONS", "Drag", "_EventInterface9", "_Components2$Directio2", "basePosition", "baseEvent", "prevBaseEvent", "isFree", "dragging", "exceeded", "clickPrevented", "disabled", "onPointerDown", "onClick", "drag", "disable", "is<PERSON><PERSON>ch", "isTouchEvent", "isDraggable", "onPointerMove", "onPointerUp", "save", "constrain", "diffCoord", "expired", "diffTime", "hasExceeded", "isSliderDirection", "shouldStart", "velocity", "computeVelocity", "computeDestination", "thresholds", "isObj", "mouse", "touch", "orthogonal", "coordOf", "getBaseEvent", "target2", "noDrag", "isDragging", "NORMALIZATION_MAP", "normalizeKey", "KEYBOARD_EVENT", "Keyboard", "_EventInterface10", "keyboard", "onKeydown", "_disabled", "SRC_DATA_ATTRIBUTE", "SRCSET_DATA_ATTRIBUTE", "IMAGE_SELECTOR", "LazyLoad", "_EventInterface11", "off", "isSequential", "entries", "loadNext", "check", "src", "srcset", "spinner", "load", "onLoad", "Pagination", "paginationClasses", "createPagination", "getDirection", "li", "button", "text", "dir", "nextPage", "item", "_button", "TRIGGER_KEYS", "Sync", "sync", "navigate", "splide", "Wheel", "_EventInterface12", "lastTime", "onWheel", "deltaY", "timeStamp", "_min", "sleep", "shouldPrevent", "SR_REMOVAL_DELAY", "Live", "_EventInterface13", "sr", "ComponentConstructors", "I18N", "DEFAULTS", "Fade", "done", "transition", "endCallback", "speed", "getSpeed", "rewindSpeed", "_Splide", "_proto", "Extensions", "_this", "Constructors", "Component", "component", "_this$event", "Splide", "querySelector", "querySelectorAll", "getElementById", "createElement", "tagName", "innerHTML", "element", "append<PERSON><PERSON><PERSON>", "prepend<PERSON>hild", "removeElement", "setStyles", "property", "MOBILE_DETECTION", "SPLIDE_CONFIG", "ADVANCED_SPLIDE_CONFIG", "ERROR_HANDLING", "ARRAY_CONSTANTS", "TIMING", "DOM_ELEMENTS", "EXTENSION_CONFIG", "isMobileDevice", "userAgent", "defaultConfig", "EXTENSION_ID", "getForumAttribute", "forum", "app", "attrFn", "getSetting", "<PERSON><PERSON><PERSON>", "defaultValue", "BOOLEAN_TRUE_VALUE", "numValue", "getAdvancedSplideConfig", "defaults", "TagTilesManager", "DOMUtils.getElementById", "tagTiles", "DOMUtils.querySelectorAll", "attempts", "checkAndProcess", "DOMUtils.createElement", "textContainer", "DOMUtils.appendChild", "isMobile", "tagElement", "tagData", "linkElement", "nameElement", "desc<PERSON><PERSON>", "backgroundImage", "computedStyle", "background", "description", "descColor", "tagUrl", "parts", "tIndex", "tagsIndex", "slug", "bgUrl", "inlineBackground", "tagModel", "tagItem", "tagRecord", "tagSlug", "innerClass", "backgroundStyle", "hasBackgroundImage", "textContent", "contentElement", "DOMUtils.querySelector", "DOMUtils.prependChild", "titleElement", "socialButtons", "extensionId", "platform", "url", "iconUrl", "marginStyle", "DOMUtils.removeElement", "appContent", "DOMUtils.setStyles", "advancedConfig", "hasEnoughSlides", "shouldEnableLoop", "autoplayConfig", "MOBILE_PER_PAGE", "perPageValue", "slideType", "splideInstance", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "context", "error", "ConfigManager", "socialPlatforms", "icon", "<PERSON><PERSON><PERSON><PERSON>", "config<PERSON><PERSON><PERSON>", "tagTilesManager", "extend", "TagsPage", "_vnode"], "mappings": "iCAAA,SAASA,GAAkBC,EAAQC,EAAO,CAAE,QAASC,EAAI,EAAGA,EAAID,EAAM,OAAQC,IAAK,CAAE,IAAIC,EAAaF,EAAMC,CAAC,EAAGC,EAAW,WAAaA,EAAW,YAAc,GAAOA,EAAW,aAAe,GAAU,UAAWA,IAAYA,EAAW,SAAW,IAAM,OAAO,eAAeH,EAAQG,EAAW,IAAKA,CAAU,CAAG,CAAE,CAE5T,SAASC,GAAaC,EAAaC,EAAYC,EAAa,CAAE,OAAID,GAAYP,GAAkBM,EAAY,UAAWC,CAAU,EAAiE,OAAO,eAAeD,EAAa,YAAa,CAAE,SAAU,GAAO,EAAUA,CAAa,CAE5R;AAAA;AAAA;AAAA;AAAA;AAAA,GAMA,IAAIG,GAA+B,mCAC/BC,GAAU,EACVC,GAAU,EACVC,GAAO,EACPC,GAAS,EACTC,GAAY,EACZC,GAAW,EACXC,GAAY,EACZC,GAAS,CACX,QAASP,GACT,QAASC,GACT,KAAMC,GACN,OAAQC,GACR,UAAWC,GACX,SAAUC,GACV,UAAWC,EACb,EAEA,SAASE,GAAMC,EAAO,CACpBA,EAAM,OAAS,CACjB,CAEA,SAASC,GAAMC,EAAWC,EAAOC,EAAK,CACpC,OAAO,MAAM,UAAU,MAAM,KAAKF,EAAWC,EAAOC,CAAG,CACzD,CAEA,SAASC,EAAMC,EAAM,CACnB,OAAOA,EAAK,KAAK,MAAMA,EAAM,CAAC,IAAI,EAAE,OAAOL,GAAM,UAAW,CAAC,CAAC,CAAC,CACjE,CAEA,IAAIM,GAAW,WAEXC,GAAO,UAAgB,CAAC,EAE5B,SAASC,GAAIH,EAAM,CACjB,OAAO,sBAAsBA,CAAI,CACnC,CAEA,SAASI,GAAOC,EAAMC,EAAS,CAC7B,OAAO,OAAOA,IAAYD,CAC5B,CAEA,SAASE,GAASD,EAAS,CACzB,MAAO,CAACE,GAAOF,CAAO,GAAKF,GAAO,SAAUE,CAAO,CACrD,CAEA,IAAIG,GAAU,MAAM,QAChBC,GAAaX,EAAMK,GAAQ,UAAU,EACrCO,GAAWZ,EAAMK,GAAQ,QAAQ,EACjCQ,GAAcb,EAAMK,GAAQ,WAAW,EAE3C,SAASI,GAAOF,EAAS,CACvB,OAAOA,IAAY,IACrB,CAEA,SAASO,GAAcP,EAAS,CAC9B,GAAI,CACF,OAAOA,aAAoBA,EAAQ,cAAc,aAAe,QAAQ,WAC1E,MAAY,CACV,MAAO,EACT,CACF,CAEA,SAASQ,GAAQC,EAAO,CACtB,OAAON,GAAQM,CAAK,EAAIA,EAAQ,CAACA,CAAK,CACxC,CAEA,SAASC,GAAQC,EAAQC,EAAU,CACjCJ,GAAQG,CAAM,EAAE,QAAQC,CAAQ,CAClC,CAEA,SAASC,GAASzB,EAAOqB,EAAO,CAC9B,OAAOrB,EAAM,QAAQqB,CAAK,EAAI,EAChC,CAEA,SAASK,GAAK1B,EAAO2B,EAAO,CAC1B,OAAA3B,EAAM,KAAK,MAAMA,EAAOoB,GAAQO,CAAK,CAAC,EAC/B3B,CACT,CAEA,SAAS4B,GAAYC,EAAKC,EAASC,EAAK,CAClCF,GACFP,GAAQQ,EAAS,SAAUE,EAAM,CAC3BA,GACFH,EAAI,UAAUE,EAAM,MAAQ,QAAQ,EAAEC,CAAI,CAE9C,CAAC,CAEL,CAEA,SAASC,GAASJ,EAAKC,EAAS,CAC9BF,GAAYC,EAAKZ,GAASa,CAAO,EAAIA,EAAQ,MAAM,GAAG,EAAIA,EAAS,EAAI,CACzE,CAEA,SAASI,GAAOC,EAAQC,EAAU,CAChCd,GAAQc,EAAUD,EAAO,YAAY,KAAKA,CAAM,CAAC,CACnD,CAEA,SAASE,GAAOC,EAAOC,EAAK,CAC1BjB,GAAQgB,EAAO,SAAUE,EAAM,CAC7B,IAAIL,GAAUI,GAAOC,GAAM,WAEvBL,GACFA,EAAO,aAAaK,EAAMD,CAAG,CAEjC,CAAC,CACH,CAEA,SAASE,GAAQZ,EAAKa,EAAU,CAC9B,OAAOvB,GAAcU,CAAG,IAAMA,EAAI,mBAAwBA,EAAI,SAAS,KAAKA,EAAKa,CAAQ,CAC3F,CAEA,SAASN,GAASD,EAAQO,EAAU,CAClC,IAAIC,EAAYR,EAASlC,GAAMkC,EAAO,QAAQ,EAAI,CAAA,EAClD,OAAOO,EAAWC,EAAU,OAAO,SAAUC,EAAO,CAClD,OAAOH,GAAQG,EAAOF,CAAQ,CAChC,CAAC,EAAIC,CACP,CAEA,SAASC,GAAMT,EAAQO,EAAU,CAC/B,OAAOA,EAAWN,GAASD,EAAQO,CAAQ,EAAE,CAAC,EAAIP,EAAO,iBAC3D,CAEA,IAAIU,GAAU,OAAO,KAErB,SAASC,GAAOC,EAAQvB,EAAUwB,EAAO,CACvC,OAAID,IACDC,EAAQH,GAAQE,CAAM,EAAE,QAAO,EAAKF,GAAQE,CAAM,GAAG,QAAQ,SAAUE,EAAK,CAC3EA,IAAQ,aAAezB,EAASuB,EAAOE,CAAG,EAAGA,CAAG,CAClD,CAAC,EAGIF,CACT,CAEA,SAASG,GAAOH,EAAQ,CACtB,OAAA9C,GAAM,UAAW,CAAC,EAAE,QAAQ,SAAUkD,EAAQ,CAC5CL,GAAOK,EAAQ,SAAU9B,EAAO4B,EAAK,CACnCF,EAAOE,CAAG,EAAIE,EAAOF,CAAG,CAC1B,CAAC,CACH,CAAC,EACMF,CACT,CAEA,SAASK,GAAML,EAAQ,CACrB,OAAA9C,GAAM,UAAW,CAAC,EAAE,QAAQ,SAAUkD,EAAQ,CAC5CL,GAAOK,EAAQ,SAAU9B,EAAO4B,EAAK,CAC/BlC,GAAQM,CAAK,EACf0B,EAAOE,CAAG,EAAI5B,EAAM,MAAK,EAChBR,GAASQ,CAAK,EACvB0B,EAAOE,CAAG,EAAIG,GAAM,CAAA,EAAIvC,GAASkC,EAAOE,CAAG,CAAC,EAAIF,EAAOE,CAAG,EAAI,CAAA,EAAI5B,CAAK,EAEvE0B,EAAOE,CAAG,EAAI5B,CAElB,CAAC,CACH,CAAC,EACM0B,CACT,CAEA,SAASM,GAAKN,EAAQO,EAAM,CAC1BhC,GAAQgC,GAAQT,GAAQE,CAAM,EAAG,SAAUE,EAAK,CAC9C,OAAOF,EAAOE,CAAG,CACnB,CAAC,CACH,CAEA,SAASM,GAAgBC,EAAMC,EAAO,CACpCnC,GAAQkC,EAAM,SAAU3B,EAAK,CAC3BP,GAAQmC,EAAO,SAAUC,EAAM,CAC7B7B,GAAOA,EAAI,gBAAgB6B,CAAI,CACjC,CAAC,CACH,CAAC,CACH,CAEA,SAASC,EAAaH,EAAMC,EAAOpC,EAAO,CACpCR,GAAS4C,CAAK,EAChBX,GAAOW,EAAO,SAAUG,EAAQ5B,EAAM,CACpC2B,EAAaH,EAAMxB,EAAM4B,CAAM,CACjC,CAAC,EAEDtC,GAAQkC,EAAM,SAAU3B,EAAK,CAC3Bf,GAAOO,CAAK,GAAKA,IAAU,GAAKkC,GAAgB1B,EAAK4B,CAAK,EAAI5B,EAAI,aAAa4B,EAAO,OAAOpC,CAAK,CAAC,CACrG,CAAC,CAEL,CAEA,SAASwC,GAAOC,EAAKL,EAAOtB,EAAQ,CAClC,IAAIN,EAAM,SAAS,cAAciC,CAAG,EAEpC,OAAIL,IACFxC,GAASwC,CAAK,EAAIxB,GAASJ,EAAK4B,CAAK,EAAIE,EAAa9B,EAAK4B,CAAK,GAGlEtB,GAAUD,GAAOC,EAAQN,CAAG,EACrBA,CACT,CAEA,SAASkC,GAAMlC,EAAKmC,EAAM3C,EAAO,CAC/B,GAAIH,GAAYG,CAAK,EACnB,OAAO,iBAAiBQ,CAAG,EAAEmC,CAAI,EAG9BlD,GAAOO,CAAK,IACfQ,EAAI,MAAMmC,CAAI,EAAI,GAAK3C,EAE3B,CAEA,SAAS4C,GAAQpC,EAAKqC,EAAU,CAC9BH,GAAMlC,EAAK,UAAWqC,CAAQ,CAChC,CAEA,SAASC,GAAMtC,EAAK,CAClBA,EAAI,WAAgBA,EAAI,UAAY,GAAMA,EAAI,MAAM,CAClD,cAAe,EACnB,CAAG,CACH,CAEA,SAASuC,GAAavC,EAAK6B,EAAM,CAC/B,OAAO7B,EAAI,aAAa6B,CAAI,CAC9B,CAEA,SAASW,GAASxC,EAAKyC,EAAW,CAChC,OAAOzC,GAAOA,EAAI,UAAU,SAASyC,CAAS,CAChD,CAEA,SAASC,GAAKzF,EAAQ,CACpB,OAAOA,EAAO,sBAAqB,CACrC,CAEA,SAAS0F,GAAOlC,EAAO,CACrBhB,GAAQgB,EAAO,SAAUE,EAAM,CACzBA,GAAQA,EAAK,YACfA,EAAK,WAAW,YAAYA,CAAI,CAEpC,CAAC,CACH,CAEA,SAASiC,GAAUC,EAAM,CACvB,OAAO9B,GAAM,IAAI,UAAS,EAAG,gBAAgB8B,EAAM,WAAW,EAAE,IAAI,CACtE,CAEA,SAASC,GAAQ,EAAGC,EAAiB,CACnC,EAAE,eAAc,EAEZA,IACF,EAAE,gBAAe,EACjB,EAAE,yBAAwB,EAE9B,CAEA,SAASC,GAAM1C,EAAQO,EAAU,CAC/B,OAAOP,GAAUA,EAAO,cAAcO,CAAQ,CAChD,CAEA,SAASoC,GAAS3C,EAAQO,EAAU,CAClC,OAAOA,EAAWzC,GAAMkC,EAAO,iBAAiBO,CAAQ,CAAC,EAAI,CAAA,CAC/D,CAEA,SAASqC,GAAYlD,EAAKC,EAAS,CACjCF,GAAYC,EAAKC,EAAS,EAAK,CACjC,CAEA,SAASkD,GAAO,EAAG,CACjB,OAAO,EAAE,SACX,CAEA,SAASC,GAAK5D,EAAO,CACnB,OAAOJ,GAASI,CAAK,EAAIA,EAAQA,EAAQA,EAAQ,KAAO,EAC1D,CAEA,IAAI6D,GAAe,SACfC,GAAiB,QAAUD,GAE/B,SAASE,GAAOC,EAAWC,EAAS,CAClC,GAAI,CAACD,EACH,MAAM,IAAI,MAAM,IAAMH,GAAe,MAAQI,GAAW,GAAG,CAE/D,CAEA,IAAIC,GAAM,KAAK,IACXC,GAAM,KAAK,IACXC,GAAQ,KAAK,MACbC,GAAO,KAAK,KACZC,EAAM,KAAK,IAEf,SAASC,GAAmBC,EAAGC,EAAGC,EAAS,CACzC,OAAOJ,EAAIE,EAAIC,CAAC,EAAIC,CACtB,CAEA,SAASC,GAAQC,EAAQJ,EAAGC,EAAGI,EAAW,CACxC,IAAIC,EAAUZ,GAAIM,EAAGC,CAAC,EAClBM,EAAUZ,GAAIK,EAAGC,CAAC,EACtB,OAAOI,EAAYC,EAAUF,GAAUA,EAASG,EAAUD,GAAWF,GAAUA,GAAUG,CAC3F,CAEA,SAASC,GAAMJ,EAAQJ,EAAGC,EAAG,CAC3B,IAAIK,EAAUZ,GAAIM,EAAGC,CAAC,EAClBM,EAAUZ,GAAIK,EAAGC,CAAC,EACtB,OAAOP,GAAIC,GAAIW,EAASF,CAAM,EAAGG,CAAO,CAC1C,CAEA,SAASE,GAAKT,EAAG,CACf,MAAO,EAAEA,EAAI,GAAK,EAAEA,EAAI,EAC1B,CAMA,SAASU,GAAOC,EAAQC,EAAc,CACpC,OAAAnF,GAAQmF,EAAc,SAAUC,EAAa,CAC3CF,EAASA,EAAO,QAAQ,KAAM,GAAKE,CAAW,CAChD,CAAC,EACMF,CACT,CAEA,SAASG,GAAIV,EAAQ,CACnB,OAAOA,EAAS,GAAK,IAAMA,EAAS,GAAKA,CAC3C,CAEA,IAAIW,GAAM,CAAA,EAEV,SAASC,GAASC,EAAQ,CACxB,MAAO,GAAKA,EAASH,GAAIC,GAAIE,CAAM,GAAKF,GAAIE,CAAM,GAAK,GAAK,CAAC,CAC/D,CAEA,SAASC,IAAc,CACrB,IAAIC,EAAY,CAAA,EAEhB,SAASC,EAAKC,EAASC,EAAQC,EAAUC,EAAS,CAChDC,EAAaJ,EAASC,EAAQ,SAAUrI,EAAQyI,EAAOC,EAAW,CAChE,IAAIC,EAAiB,qBAAsB3I,EACvC4I,EAAUD,EAAgB3I,EAAO,oBAAoB,KAAKA,EAAQyI,EAAOH,EAAUC,CAAO,EAAIvI,EAAO,eAAkB,KAAKA,EAAQsI,CAAQ,EAChJK,EAAgB3I,EAAO,iBAAiByI,EAAOH,EAAUC,CAAO,EAAIvI,EAAO,YAAesI,CAAQ,EAClGJ,EAAU,KAAK,CAAClI,EAAQyI,EAAOC,EAAWJ,EAAUM,CAAO,CAAC,CAC9D,CAAC,CACH,CAEA,SAASC,EAAOT,EAASC,EAAQC,EAAU,CACzCE,EAAaJ,EAASC,EAAQ,SAAUrI,EAAQyI,EAAOC,EAAW,CAChER,EAAYA,EAAU,OAAO,SAAUY,EAAU,CAC/C,OAAIA,EAAS,CAAC,IAAM9I,GAAU8I,EAAS,CAAC,IAAML,GAASK,EAAS,CAAC,IAAMJ,IAAc,CAACJ,GAAYQ,EAAS,CAAC,IAAMR,IAChHQ,EAAS,CAAC,EAAC,EACJ,IAGF,EACT,CAAC,CACH,CAAC,CACH,CAEA,SAASC,EAAS/I,EAAQ6B,EAAMmH,EAAQ,CACtC,IAAIC,EACAC,EAAU,GAEd,OAAI,OAAO,aAAgB,WACzBD,EAAI,IAAI,YAAYpH,EAAM,CACxB,QAASqH,EACT,OAAQF,CAChB,CAAO,GAEDC,EAAI,SAAS,YAAY,aAAa,EACtCA,EAAE,gBAAgBpH,EAAMqH,EAAS,GAAOF,CAAM,GAGhDhJ,EAAO,cAAciJ,CAAC,EACfA,CACT,CAEA,SAAST,EAAaJ,EAASC,EAAQ3F,EAAU,CAC/CF,GAAQ4F,EAAS,SAAUpI,EAAQ,CACjCA,GAAUwC,GAAQ6F,EAAQ,SAAUc,EAAS,CAC3CA,EAAQ,MAAM,GAAG,EAAE,QAAQ,SAAUC,EAAS,CAC5C,IAAIC,EAAWD,EAAQ,MAAM,GAAG,EAChC1G,EAAS1C,EAAQqJ,EAAS,CAAC,EAAGA,EAAS,CAAC,CAAC,CAC3C,CAAC,CACH,CAAC,CACH,CAAC,CACH,CAEA,SAASC,GAAU,CACjBpB,EAAU,QAAQ,SAAUqB,EAAM,CAChCA,EAAK,CAAC,EAAC,CACT,CAAC,EACDtI,GAAMiH,CAAS,CACjB,CAEA,MAAO,CACL,KAAMC,EACN,OAAQU,EACR,SAAUE,EACV,QAASO,CACb,CACA,CAEA,IAAIE,GAAgB,UAChBC,GAAc,QACdC,GAAa,OACbC,GAAc,QACdC,GAAc,QACdC,GAAe,SACfC,GAAiB,WACjBC,GAAgB,UAChBC,GAAe,SACfC,EAAgB,UAChBC,EAAgB,UAChBC,GAAe,SACfC,GAAgB,UAChBC,GAAa,OACbC,GAAiB,WACjBC,GAAgB,UAChBC,GAAe,SACfC,GAAiB,WACjBC,GAAiB,WACjBC,GAAgB,UAChBC,GAAuB,iBACvBC,GAAuB,iBACvBC,GAA2B,qBAC3BC,GAA2B,qBAC3BC,GAA2B,qBAC3BC,GAAsB,gBACtBC,GAAyB,mBACzBC,GAAuB,iBACvBC,GAAwB,kBACxBC,GAAsB,KACtBC,GAAgB,KAChBC,GAA0B,KAE9B,SAASC,EAAeC,EAAS,CAC/B,IAAIC,EAAMD,EAAUA,EAAQ,MAAM,IAAM,SAAS,uBAAsB,EACnEE,EAAS1D,GAAW,EAExB,SAAS2D,EAAGvD,EAAQC,EAAU,CAC5BqD,EAAO,KAAKD,EAAKpJ,GAAQ+F,CAAM,EAAE,KAAK,GAAG,EAAG,SAAUY,EAAG,CACvDX,EAAS,MAAMA,EAAUrG,GAAQgH,EAAE,MAAM,EAAIA,EAAE,OAAS,EAAE,CAC5D,CAAC,CACH,CAEA,SAAS4C,EAAKpD,EAAO,CACnBkD,EAAO,SAASD,EAAKjD,EAAOtH,GAAM,UAAW,CAAC,CAAC,CACjD,CAEA,OAAIsK,GACFA,EAAQ,MAAM,GAAGd,GAAegB,EAAO,OAAO,EAGzCvH,GAAOuH,EAAQ,CACpB,IAAKD,EACL,GAAIE,EACJ,IAAKrK,EAAMoK,EAAO,OAAQD,CAAG,EAC7B,KAAMG,CACV,CAAG,CACH,CAEA,SAASC,GAAgBC,EAAUC,EAAYC,EAAUC,EAAO,CAC9D,IAAIC,EAAM,KAAK,IACXC,EACAC,EAAO,EACPC,EACAC,EAAS,GACTC,EAAQ,EAEZ,SAASC,GAAS,CAChB,GAAI,CAACF,EAAQ,CAIX,GAHAF,EAAON,EAAWtF,IAAK0F,EAAG,EAAKC,GAAaL,EAAU,CAAC,EAAI,EAC3DE,GAAYA,EAASI,CAAI,EAErBA,GAAQ,IACVL,EAAU,EACVI,EAAYD,EAAG,EAEXD,GAAS,EAAEM,GAASN,GACtB,OAAOQ,EAAK,EAIhBJ,EAAK3K,GAAI8K,CAAM,CACjB,CACF,CAEA,SAASpL,EAAMsL,EAAQ,CACrBA,GAAUC,EAAM,EAChBR,EAAYD,EAAG,GAAMQ,EAASN,EAAON,EAAW,GAChDQ,EAAS,GACTD,EAAK3K,GAAI8K,CAAM,CACjB,CAEA,SAASC,GAAQ,CACfH,EAAS,EACX,CAEA,SAASM,GAAS,CAChBT,EAAYD,EAAG,EACfE,EAAO,EAEHJ,GACFA,EAASI,CAAI,CAEjB,CAEA,SAASO,GAAS,CAChBN,GAAM,qBAAqBA,CAAE,EAC7BD,EAAO,EACPC,EAAK,EACLC,EAAS,EACX,CAEA,SAASO,EAAIC,EAAM,CACjBhB,EAAWgB,CACb,CAEA,SAASC,GAAW,CAClB,OAAOT,CACT,CAEA,MAAO,CACL,MAAOlL,EACP,OAAQwL,EACR,MAAOH,EACP,OAAQE,EACR,IAAKE,EACL,SAAUE,CACd,CACA,CAEA,SAASC,GAAMC,EAAc,CAC3B,IAAIC,EAAQD,EAEZ,SAASJ,EAAIvK,EAAO,CAClB4K,EAAQ5K,CACV,CAEA,SAAS6K,EAAGC,EAAQ,CAClB,OAAO1K,GAASL,GAAQ+K,CAAM,EAAGF,CAAK,CACxC,CAEA,MAAO,CACL,IAAKL,EACL,GAAIM,CACR,CACA,CAEA,SAASE,GAAS9L,EAAM+L,EAAU,CAChC,IAAIxB,EAAWD,GAA4B,EAAGtK,EAAM,KAAM,CAAC,EAC3D,OAAO,UAAY,CACjBuK,EAAS,SAAQ,GAAMA,EAAS,MAAK,CACvC,CACF,CAEA,SAASyB,GAAM/B,EAASgC,EAAalF,EAAS,CAC5C,IAAI4E,EAAQ1B,EAAQ,MAChBiC,EAAcnF,EAAQ,aAAe,CAAA,EACrCoF,EAAgBpF,EAAQ,eAAiB,CAAA,EACzCoD,EAAS1D,GAAW,EACpB2F,EAAU,CAAA,EAEd,SAASC,GAAQ,CACf,IAAIC,EAAQvF,EAAQ,aAAe,MACnCxE,GAAQ2J,CAAW,EAAE,KAAK,SAAUK,EAAGC,EAAG,CACxC,OAAOF,EAAQ,CAACC,EAAI,CAACC,EAAI,CAACA,EAAI,CAACD,CACjC,CAAC,EAAE,QAAQ,SAAU5J,EAAK,CACxB8J,EAASP,EAAYvJ,CAAG,EAAG,KAAO2J,EAAQ,MAAQ,OAAS,UAAY3J,EAAM,KAAK,CACpF,CAAC,EACD8J,EAASN,EAAenN,EAA4B,EACpDiM,EAAM,CACR,CAEA,SAASnD,EAAQ4E,EAAY,CACvBA,GACFvC,EAAO,QAAO,CAElB,CAEA,SAASsC,EAASE,EAAUpI,EAAO,CACjC,IAAIqI,EAAY,WAAWrI,CAAK,EAChC4F,EAAO,KAAKyC,EAAW,SAAU3B,CAAM,EACvCmB,EAAQ,KAAK,CAACO,EAAUC,CAAS,CAAC,CACpC,CAEA,SAAS3B,GAAS,CAChB,IAAI4B,EAAYlB,EAAM,GAAGpM,EAAS,EAC9BuN,EAAY/F,EAAQ,UACpBgG,EAASX,EAAQ,OAAO,SAAUY,EAASC,EAAO,CACpD,OAAOnK,GAAMkK,EAASC,EAAM,CAAC,EAAE,QAAUA,EAAM,CAAC,EAAI,EAAE,CACxD,EAAG,CAAA,CAAE,EACLlK,GAAKgE,CAAO,EACZuE,EAAIyB,CAAM,EAENhG,EAAQ,QACVkD,EAAQ,QAAQlD,EAAQ,UAAY,YAAY,EACvC8F,GACT/E,EAAQ,EAAI,EACZmC,EAAQ,MAAK,GAEb6C,IAAc/F,EAAQ,WAAakD,EAAQ,QAAO,CAEtD,CAEA,SAASiD,EAAOC,EAAQ,CAClB,WAAWnO,EAA4B,EAAE,UAC3CmO,EAASrK,GAAMiE,EAASoF,CAAa,EAAIpJ,GAAKgE,EAASxE,GAAQ4J,CAAa,CAAC,EAEjF,CAEA,SAASb,EAAI8B,EAAMC,EAAMC,EAAQ,CAC/BxK,GAAMiE,EAASqG,CAAI,EACnBC,GAAQvK,GAAM,OAAO,eAAeiE,CAAO,EAAGqG,CAAI,GAE9CE,GAAU,CAAC3B,EAAM,GAAG1M,EAAO,IAC7BgL,EAAQ,KAAKvB,EAAe3B,CAAO,CAEvC,CAEA,MAAO,CACL,MAAOsF,EACP,QAASvE,EACT,OAAQoF,EACR,IAAK5B,CACT,CACA,CAEA,IAAIiC,GAAQ,QACRC,GAAaD,GAAQ,OACrBE,GAAcF,GAAQ,QACtBG,GAAWH,GAAQ,KACnBI,GAAaJ,GAAQ,OAErBK,GAAM,MACNC,GAAM,MACNC,GAAkB,CACpB,MAAO,CAAC,QAAQ,EAChB,KAAM,CAAC,MAAO,OAAO,EACrB,MAAO,CAAC,SAAU,MAAM,EACxB,EAAG,CAAC,GAAG,EACP,EAAG,CAAC,GAAG,EACP,EAAG,CAAC,GAAG,EACP,UAAW,CAACJ,GAAUD,EAAW,EACjC,WAAY,CAACE,GAAYH,EAAU,CACrC,EAEA,SAASO,GAAU9D,EAASgC,EAAalF,EAAS,CAChD,SAASiH,EAAQtK,EAAMuK,EAAUnB,EAAW,CAC1CA,EAAYA,GAAa/F,EAAQ,UACjC,IAAImH,EAAQpB,IAAcc,IAAO,CAACK,EAAW,EAAInB,IAAce,GAAM,EAAI,GACzE,OAAOC,GAAgBpK,CAAI,GAAKoK,GAAgBpK,CAAI,EAAEwK,CAAK,GAAKxK,EAAK,QAAQ,oBAAqB,SAAUyK,EAAOC,EAAQ,CACzH,IAAIhI,EAAc0H,GAAgBK,EAAM,YAAW,CAAE,EAAED,CAAK,GAAKC,EACjE,OAAOC,EAAS,EAAIhI,EAAY,OAAO,CAAC,EAAE,YAAW,EAAKA,EAAY,MAAM,CAAC,EAAIA,CACnF,CAAC,CACH,CAEA,SAASiI,EAAOtN,EAAO,CACrB,OAAOA,GAASgG,EAAQ,YAAc6G,GAAM,EAAI,GAClD,CAEA,MAAO,CACL,QAASI,EACT,OAAQK,CACZ,CACA,CAEA,IAAIC,GAAO,OACPC,GAAY,WACZC,GAAW,WACXC,GAAc,QACdC,GAAgBD,GAAc,WAC9BE,GAAeF,GAAc,UAC7BG,GAAgBH,GAAc,WAC9BI,GAAaJ,GAAc,QAC3BK,GAAkBL,GAAc,aAChCM,GAAcN,GAAc,SAC5BO,GAAmBP,GAAc,cACjCQ,GAAuBR,GAAc,kBACrCS,GAAYT,GAAc,OAC1BU,GAAYV,GAAc,OAC1BW,GAAcX,GAAc,SAC5BY,GAAiB,CAACf,GAAMC,GAAWC,GAAUE,GAAeC,GAAcE,GAAYC,GAAiBC,GAAaC,GAAkBC,EAAoB,EAC1JK,GAAe1K,GAAe,KAC9B2K,GAAsB,MACtBC,GAAa5K,GACb6K,GAAcH,GAAe,QAC7BI,GAAaJ,GAAe,OAC5BK,GAAcL,GAAe,QAC7BM,GAAcD,GAAc,UAC5BE,GAAkBF,GAAc,cAChCG,GAAeR,GAAe,SAC9BS,GAAcT,GAAe,QAC7BU,GAAmBD,GAAc,SACjCE,GAAmBF,GAAc,SACjCG,GAAmBZ,GAAe,aAClCa,GAAwBD,GAAmB,SAC3CE,GAAiBd,GAAe,WAChCe,GAAqBD,GAAiB,QACtCE,GAAehB,GAAe,SAG9BiB,GAAgBjB,GAAe,UAC/BkB,GAAWlB,GAAe,KAC1BmB,GAAoBlB,GAAsB,cAC1CmB,GAAenB,GAAsB,SACrCoB,GAAapB,GAAsB,OACnCqB,GAAarB,GAAsB,OACnCsB,GAAgBtB,GAAsB,UACtCuB,GAAgBvB,GAAsB,UACtCwB,GAAiBxB,GAAsB,WACvCyB,GAAiBzB,GAAsB,WACvC0B,GAAiB,CAACP,GAAcG,GAAeF,GAAYC,GAAYE,GAAeC,GAAgBC,EAAc,EACpHE,GAAU,CACZ,MAAOvB,GACP,MAAOC,GACP,OAAQE,GACR,MAAOC,GACP,KAAMC,GACN,KAAMC,GACN,WAAYC,GACZ,KAAMC,GACN,QAASI,EACX,EAEA,SAASY,GAAQC,EAAMhP,EAAU,CAC/B,GAAI1B,GAAW0Q,EAAK,OAAO,EACzB,OAAOA,EAAK,QAAQhP,CAAQ,EAK9B,QAFIb,EAAM6P,EAEH7P,GAAOA,EAAI,WAAa,GACzB,CAAAY,GAAQZ,EAAKa,CAAQ,GAIzBb,EAAMA,EAAI,cAGZ,OAAOA,CACT,CAEA,IAAI8P,GAAW,EACXC,GAAe,IACfC,GAAsB,uBACtBC,GAAsB,sBACtBC,GAAoB,qCAExB,SAASC,GAASzH,EAASgC,EAAalF,EAAS,CAC/C,IAAI4K,EAAkB3H,EAAeC,CAAO,EACxCG,EAAKuH,EAAgB,GACrBhL,EAAOgL,EAAgB,KAEvBC,EAAO3H,EAAQ,KACf4H,EAAO9K,EAAQ,KACf+K,EAAW,CAAA,EACXC,EAAS,CAAA,EACTC,EAAc,CAAA,EACdC,EAAe,CAAA,EACfC,EACAC,EACAC,EAEJ,SAAS/F,GAAQ,CACfgG,EAAO,EACPC,EAAI,EACJrH,EAAM,CACR,CAEA,SAASsH,GAAQ,CACfnI,EAAG3B,EAAeX,CAAO,EACzBsC,EAAG3B,EAAe4D,CAAK,EACvBjC,EAAG1B,EAAeuC,CAAM,EACxBtE,EAAK,SAAU4K,GAAsB,WAAY,SAAU9J,EAAG,CAC5D2K,EAAa3K,EAAE,OAAS,SAC1B,EAAG,CACD,QAAS,EACf,CAAK,EACDd,EAAKiL,EAAM,UAAW,UAAY,CAChCtQ,GAAYsQ,EAAMb,GAAgB,CAAC,CAACqB,CAAU,CAChD,CAAC,CACH,CAEA,SAAStK,EAAQ4E,EAAY,CAC3B,IAAIvJ,EAAQkM,GAAe,OAAO,OAAO,EACzC5P,GAAMsS,CAAM,EACZtN,GAAYmN,EAAMI,CAAW,EAC7BvN,GAAYyN,EAAOD,CAAY,EAC/BhP,GAAgB,CAACiP,EAAOC,CAAI,EAAGhP,CAAK,EACpCF,GAAgB2O,EAAMlF,EAAavJ,EAAQ,CAAC,QAAS8L,EAAoB,CAAC,CAC5E,CAEA,SAAShE,GAAS,CAChBxG,GAAYmN,EAAMI,CAAW,EAC7BvN,GAAYyN,EAAOD,CAAY,EAC/BD,EAAcQ,EAAWhD,EAAU,EACnCyC,EAAeO,EAAW/C,EAAW,EACrC9N,GAASiQ,EAAMI,CAAW,EAC1BrQ,GAASuQ,EAAOD,CAAY,EAC5B5O,EAAauO,EAAM/C,GAAY9H,EAAQ,KAAK,EAC5C1D,EAAauO,EAAM9C,GAAiB/H,EAAQ,UAAU,CACxD,CAEA,SAASsL,GAAU,CACjBH,EAAQO,EAAK,IAAMhD,EAAW,EAC9B0C,EAAO7P,GAAM4P,EAAO,IAAMxC,EAAU,EACpC5K,GAAOoN,GAASC,EAAM,kCAAkC,EACxD/Q,GAAK2Q,EAAQjQ,GAASqQ,EAAM,IAAMxC,GAAc,SAAWC,GAAc,GAAG,CAAC,EAC7EpN,GAAO,CACL,OAAQsN,GACR,WAAYI,GACZ,KAAMF,GACN,KAAMC,GACN,IAAKI,GACL,OAAQC,EACd,EAAO,SAAUtM,EAAWrB,EAAK,CAC3BmP,EAASnP,CAAG,EAAI8P,EAAK,IAAMzO,CAAS,CACtC,CAAC,EACDpB,GAAOkP,EAAU,CACf,KAAMF,EACN,MAAOM,EACP,KAAMC,EACN,OAAQJ,CACd,CAAK,CACH,CAEA,SAASO,GAAO,CACd,IAAIxH,EAAK8G,EAAK,IAAMrL,GAAS3B,EAAY,EACrC8N,EAAO3L,EAAQ,KACnB6K,EAAK,GAAK9G,EACVoH,EAAM,GAAKA,EAAM,IAAMpH,EAAK,SAC5BqH,EAAK,GAAKA,EAAK,IAAMrH,EAAK,QAEtB,CAAChH,GAAa8N,EAAMtD,EAAI,GAAKsD,EAAK,UAAY,WAAac,GAC7DrP,EAAauO,EAAMtD,GAAMoE,CAAI,EAG/BrP,EAAauO,EAAM3C,GAAsB4C,EAAK,QAAQ,EACtDxO,EAAa8O,EAAM7D,GAAM,cAAc,CACzC,CAEA,SAASmE,EAAKrQ,EAAU,CACtB,IAAIb,EAAMgD,GAAMqN,EAAMxP,CAAQ,EAC9B,OAAOb,GAAO4P,GAAQ5P,EAAK,IAAMiO,EAAU,IAAMoC,EAAOrQ,EAAM,MAChE,CAEA,SAASiR,EAAWnF,EAAM,CACxB,MAAO,CAACA,EAAO,KAAOtG,EAAQ,KAAMsG,EAAO,KAAOtG,EAAQ,UAAWA,EAAQ,MAAQsG,EAAO,cAAetG,EAAQ,cAAgBsG,EAAO,QAASA,IAASmC,IAAckB,EAAY,CACxL,CAEA,OAAO9N,GAAOkP,EAAU,CACtB,MAAOzF,EACP,MAAOkG,EACP,QAASzK,CACb,CAAG,CACH,CAEA,IAAI6K,GAAQ,QACRC,GAAO,OACPC,GAAO,OAEX,SAASC,GAAQ7I,EAASiE,EAAO6E,EAAYC,EAAO,CAClD,IAAI/L,EAAQ+C,EAAeC,CAAO,EAC9BG,EAAKnD,EAAM,GACXoD,EAAOpD,EAAM,KACbN,EAAOM,EAAM,KACbgM,EAAahJ,EAAQ,WACrB2H,EAAO3H,EAAQ,KACflD,EAAUkD,EAAQ,QAClBiJ,EAAenM,EAAQ,aACvBoM,EAAepM,EAAQ,aACvB8K,EAAO9K,EAAQ,KACfqM,EAAarM,EAAQ,WACrBsM,EAAatM,EAAQ,WACrBiH,EAAUiF,EAAW,UAAU,QAC/BK,EAASxP,GAAakP,EAAO,OAAO,EACpCO,EAAQzP,GAAakP,EAAOnE,EAAU,EACtC2E,EAAUT,EAAa,GACvBU,EAAYnR,GAAM0Q,EAAO,IAAMnD,EAAe,EAC9ChD,EAEJ,SAAS0F,GAAQ,CACViB,IACHR,EAAM,GAAKpB,EAAK,GAAK,SAAWvL,GAAI6H,EAAQ,CAAC,EAC7C7K,EAAa2P,EAAO1E,GAAM8E,EAAa,WAAa,OAAO,EAC3D/P,EAAa2P,EAAO/D,GAAsB4C,EAAK,KAAK,EACpDxO,EAAa2P,EAAOnE,GAAY0E,GAAStN,GAAO4L,EAAK,WAAY,CAAC3D,EAAQ,EAAGjE,EAAQ,MAAM,CAAC,CAAC,GAG/FyJ,EAAM,CACR,CAEA,SAASA,GAAS,CAChB/M,EAAKqM,EAAO,QAASjT,EAAMsK,EAAMjC,GAAauL,CAAI,CAAC,EACnDhN,EAAKqM,EAAO,UAAWjT,EAAMsK,EAAMR,GAAqB8J,CAAI,CAAC,EAC7DvJ,EAAG,CAACjC,GAAa2B,GAAeb,EAAc,EAAGgC,CAAM,EACvDb,EAAGZ,GAA0BoK,CAAc,EAEvCT,GACF/I,EAAGlC,GAAY2L,CAAM,CAEzB,CAEA,SAAS/L,GAAU,CACjB+E,EAAY,GACZ5F,EAAM,QAAO,EACbxC,GAAYuO,EAAO/B,EAAc,EACjChO,GAAgB+P,EAAO3D,EAAc,EACrChM,EAAa2P,EAAO,QAASM,CAAM,EACnCjQ,EAAa2P,EAAOnE,GAAY0E,GAAS,EAAE,CAC7C,CAEA,SAASK,GAAiB,CACxB,IAAIE,EAAW7J,EAAQ,QAAQ,IAAI,SAAUzL,EAAQ,CACnD,IAAIuV,EAASvV,EAAO,OAAO,WAAW,OAAO,MAAM0P,CAAK,EACxD,OAAO6F,EAASA,EAAO,MAAM,GAAK,EACpC,CAAC,EAAE,KAAK,GAAG,EACX1Q,EAAa2P,EAAOnE,GAAY5I,GAAO4L,EAAK,QAAS2B,EAAUT,EAAa7E,GAAS,CAAC,CAAC,EACvF7K,EAAa2P,EAAOtE,GAAeoF,CAAQ,EAC3CzQ,EAAa2P,EAAO1E,GAAM+E,EAAa,SAAW,EAAE,EACpDA,GAAcpQ,GAAgB+P,EAAO/D,EAAoB,CAC3D,CAEA,SAAS4E,GAAS,CACXhH,GACH5B,EAAM,CAEV,CAEA,SAASA,GAAS,CAChB,GAAI,CAAC4B,EAAW,CACd,IAAImH,EAAO/J,EAAQ,MACnBgK,EAAc,EACdC,EAAgB,EAChB5S,GAAY0R,EAAOrC,GAAYzC,IAAU8F,EAAO,CAAC,EACjD1S,GAAY0R,EAAOpC,GAAY1C,IAAU8F,EAAO,CAAC,CACnD,CACF,CAEA,SAASC,GAAiB,CACxB,IAAIE,EAASC,EAAQ,EAEjBD,IAAWpQ,GAASiP,EAAOtC,EAAY,IACzCpP,GAAY0R,EAAOtC,GAAcyD,CAAM,EACvC9Q,EAAa2P,EAAOrE,GAAcuE,GAAgBiB,GAAU,EAAE,EAC9D9J,EAAK8J,EAAS9L,GAAeC,GAAgBqL,CAAI,EAErD,CAEA,SAASO,GAAmB,CAC1B,IAAIG,EAAUC,EAAS,EACnBC,EAAS,CAACF,IAAY,CAACD,EAAQ,GAAMZ,GAiBzC,GAfKvJ,EAAQ,MAAM,GAAG,CAAC7K,GAAQC,EAAS,CAAC,GACvCgE,EAAa2P,EAAOjE,GAAawF,GAAU,EAAE,EAG/ClR,EAAamB,GAASwO,EAAOjM,EAAQ,gBAAkB,EAAE,EAAGwH,GAAWgG,EAAS,GAAK,EAAE,EAEnFlB,GACFhQ,EAAa2P,EAAOzE,GAAWgG,EAAS,GAAK,CAAC,EAG5CF,IAAYtQ,GAASiP,EAAOnC,EAAa,IAC3CvP,GAAY0R,EAAOnC,GAAewD,CAAO,EACzChK,EAAKgK,EAAU9L,GAAgBC,GAAcmL,CAAI,GAG/C,CAACU,GAAW,SAAS,gBAAkBrB,EAAO,CAChD,IAAIe,EAASd,EAAW,OAAO,MAAMhJ,EAAQ,KAAK,EAClD8J,GAAUlQ,GAAMkQ,EAAO,KAAK,CAC9B,CACF,CAEA,SAASS,EAAQ9Q,EAAM3C,EAAO0T,EAAc,CAC1ChR,GAAMgR,GAAgBhB,GAAaT,EAAOtP,EAAM3C,CAAK,CACvD,CAEA,SAASqT,GAAW,CAClB,IAAIJ,EAAO/J,EAAQ,MACnB,OAAO+J,IAAS9F,GAASnH,EAAQ,aAAeiN,IAASjB,CAC3D,CAEA,SAASuB,GAAY,CACnB,GAAIrK,EAAQ,GAAG4I,EAAI,EACjB,OAAOuB,EAAQ,EAGjB,IAAIM,EAAYzQ,GAAKgP,EAAW,SAAS,KAAK,EAC1C0B,EAAY1Q,GAAK+O,CAAK,EACtB4B,EAAO5G,EAAQ,OAAQ,EAAI,EAC3BtL,EAAQsL,EAAQ,QAAS,EAAI,EACjC,OAAO7I,GAAMuP,EAAUE,CAAI,CAAC,GAAKxP,GAAKuP,EAAUC,CAAI,CAAC,GAAKzP,GAAMwP,EAAUjS,CAAK,CAAC,GAAK0C,GAAKsP,EAAUhS,CAAK,CAAC,CAC5G,CAEA,SAASmS,EAASzD,EAAM0D,EAAU,CAChC,IAAIC,EAAO1P,EAAI+L,EAAOlD,CAAK,EAE3B,MAAI,CAACsF,IAAYzM,EAAQ,QAAUkD,EAAQ,GAAG2I,EAAI,KAChDmC,EAAO9P,GAAI8P,EAAM9K,EAAQ,OAAS8K,CAAI,GAGjCA,GAAQD,CACjB,CAEA,IAAInB,EAAO,CACT,MAAOzF,EACP,WAAY6E,EACZ,MAAOC,EACP,UAAWS,EACX,QAASD,EACT,MAAOjB,EACP,QAASzK,EACT,OAAQmD,EACR,MAAOuJ,EACP,SAAUK,CACd,EACE,OAAOlB,CACT,CAEA,SAASqB,GAAO/K,EAASgC,EAAalF,EAAS,CAC7C,IAAIkO,EAAmBjL,EAAeC,CAAO,EACzCG,EAAK6K,EAAiB,GACtB5K,EAAO4K,EAAiB,KACxBtO,EAAOsO,EAAiB,KAExBC,EAAwBjJ,EAAY,SACpC8F,EAASmD,EAAsB,OAC/B/C,EAAO+C,EAAsB,KAC7BC,EAAU,CAAA,EAEd,SAAS5C,GAAQ,CACfD,EAAI,EACJlI,EAAG3B,EAAeX,CAAO,EACzBsC,EAAG3B,EAAe6J,CAAI,CACxB,CAEA,SAASA,GAAO,CACdP,EAAO,QAAQ,SAAUiB,EAAO9E,EAAO,CACrCzB,EAASuG,EAAO9E,EAAO,EAAE,CAC3B,CAAC,CACH,CAEA,SAASpG,GAAU,CACjBsN,EAAU,SAAUrB,EAAQ,CAC1BA,EAAO,QAAO,CAChB,CAAC,EACDtU,GAAM0V,CAAO,CACf,CAEA,SAASlK,GAAS,CAChBmK,EAAU,SAAUrB,EAAQ,CAC1BA,EAAO,OAAM,CACf,CAAC,CACH,CAEA,SAAStH,EAASuG,EAAO9E,EAAO6E,EAAY,CAC1C,IAAItQ,EAASqQ,GAAQ7I,EAASiE,EAAO6E,EAAYC,CAAK,EACtDvQ,EAAO,MAAK,EACZ0S,EAAQ,KAAK1S,CAAM,EACnB0S,EAAQ,KAAK,SAAUE,EAAQtB,EAAQ,CACrC,OAAOsB,EAAO,MAAQtB,EAAO,KAC/B,CAAC,CACH,CAEA,SAASuB,EAAIC,EAAe,CAC1B,OAAOA,EAAgBC,EAAO,SAAUzB,EAAQ,CAC9C,MAAO,CAACA,EAAO,OACjB,CAAC,EAAIoB,CACP,CAEA,SAASM,EAAMC,EAAM,CACnB,IAAIC,EAAa1J,EAAY,WACzBiC,EAAQyH,EAAW,QAAQD,CAAI,EAC/BxQ,EAAMyQ,EAAW,SAAQ,EAAK,EAAI5O,EAAQ,QAC9C,OAAOyO,EAAO,SAAUzB,EAAQ,CAC9B,OAAOrO,GAAQqO,EAAO,MAAO7F,EAAOA,EAAQhJ,EAAM,CAAC,CACrD,CAAC,CACH,CAEA,SAAS0Q,EAAM1H,EAAO,CACpB,OAAOsH,EAAOtH,CAAK,EAAE,CAAC,CACxB,CAEA,SAASzM,EAAIJ,EAAO6M,EAAO,CACzBlN,GAAQK,EAAO,SAAU2R,EAAO,CAK9B,GAJIrS,GAASqS,CAAK,IAChBA,EAAQ7O,GAAU6O,CAAK,GAGrBnS,GAAcmS,CAAK,EAAG,CACxB,IAAI/Q,EAAM8P,EAAO7D,CAAK,EACtBjM,EAAMF,GAAOiR,EAAO/Q,CAAG,EAAIL,GAAOuQ,EAAMa,CAAK,EAC7CrR,GAASqR,EAAOjM,EAAQ,QAAQ,KAAK,EACrC8O,EAAc7C,EAAOjT,EAAMsK,EAAM1B,EAAY,CAAC,CAChD,CACF,CAAC,EACD0B,EAAK5B,CAAa,CACpB,CAEA,SAASqN,EAASC,EAAS,CACzB7R,GAAOsR,EAAOO,CAAO,EAAE,IAAI,SAAUhC,EAAQ,CAC3C,OAAOA,EAAO,KAChB,CAAC,CAAC,EACF1J,EAAK5B,CAAa,CACpB,CAEA,SAAS2M,EAAUlU,EAAUqU,EAAe,CAC1CD,EAAIC,CAAa,EAAE,QAAQrU,CAAQ,CACrC,CAEA,SAASsU,EAAOO,EAAS,CACvB,OAAOZ,EAAQ,OAAOzU,GAAWqV,CAAO,EAAIA,EAAU,SAAUhC,EAAQ,CACtE,OAAOpT,GAASoV,CAAO,EAAI5T,GAAQ4R,EAAO,MAAOgC,CAAO,EAAI5U,GAASL,GAAQiV,CAAO,EAAGhC,EAAO,KAAK,CACrG,CAAC,CACH,CAEA,SAAStQ,EAAMC,EAAM3C,EAAO0T,EAAc,CACxCW,EAAU,SAAUrB,EAAQ,CAC1BA,EAAO,MAAMrQ,EAAM3C,EAAO0T,CAAY,CACxC,CAAC,CACH,CAEA,SAASoB,EAActU,EAAKuF,EAAU,CACpC,IAAIkP,EAASxR,GAASjD,EAAK,KAAK,EAC5B0U,EAASD,EAAO,OAEhBC,EACFD,EAAO,QAAQ,SAAUE,EAAK,CAC5BvP,EAAKuP,EAAK,aAAc,UAAY,CAC5B,EAAED,GACNnP,EAAQ,CAEZ,CAAC,CACH,CAAC,EAEDA,EAAQ,CAEZ,CAEA,SAASqP,EAAUZ,EAAe,CAChC,OAAOA,EAAgBxD,EAAO,OAASoD,EAAQ,MACjD,CAEA,SAASiB,GAAW,CAClB,OAAOjB,EAAQ,OAASpO,EAAQ,OAClC,CAEA,MAAO,CACL,MAAOwL,EACP,QAASzK,EACT,OAAQmD,EACR,SAAUwB,EACV,IAAK6I,EACL,MAAOG,EACP,MAAOG,EACP,IAAKnU,EACL,OAAQqU,EACR,QAASV,EACT,OAAQI,EACR,MAAO/R,EACP,UAAW0S,EACX,SAAUC,CACd,CACA,CAEA,SAASC,GAAOpM,EAASgC,EAAalF,EAAS,CAC7C,IAAIuP,EAAmBtM,EAAeC,CAAO,EACzCG,EAAKkM,EAAiB,GACtB3P,EAAO2P,EAAiB,KACxBjM,EAAOiM,EAAiB,KAExBtB,EAAS/I,EAAY,OACrB+B,EAAU/B,EAAY,UAAU,QAChCsK,EAAyBtK,EAAY,SACrC2F,EAAO2E,EAAuB,KAC9BrE,EAAQqE,EAAuB,MAC/BpE,EAAOoE,EAAuB,KAC9BX,EAAQZ,EAAO,MACfwB,EAAcxB,EAAO,MACrByB,EACAC,EACAC,EAEJ,SAASpE,GAAQ,CACfD,EAAI,EACJ3L,EAAK,OAAQ,cAAemF,GAAS/L,EAAMsK,EAAM1B,EAAY,CAAC,CAAC,EAC/DyB,EAAG,CAAC1B,EAAeD,CAAa,EAAG6J,CAAI,EACvClI,EAAGzB,GAAciO,CAAM,CACzB,CAEA,SAAStE,GAAO,CACdmE,EAAW1P,EAAQ,YAAc8G,GACjCpK,GAAMmO,EAAM,WAAYjN,GAAKoC,EAAQ,KAAK,CAAC,EAC3CtD,GAAMyO,EAAOlE,EAAQ,aAAa,EAAG6I,EAAW,EAAK,CAAC,EACtDpT,GAAMyO,EAAOlE,EAAQ,cAAc,EAAG6I,EAAW,EAAI,CAAC,EACtDD,EAAO,EAAI,CACb,CAEA,SAASA,EAAOE,EAAO,CACrB,IAAIC,EAAU9S,GAAK2N,CAAI,GAEnBkF,GAASJ,EAAS,QAAUK,EAAQ,OAASL,EAAS,SAAWK,EAAQ,UAC3EtT,GAAMyO,EAAO,SAAU8E,GAAgB,EACvCR,EAAYxI,EAAQ,aAAa,EAAGrJ,GAAKoC,EAAQ,GAAG,CAAC,EACrDyP,EAAY,QAASS,GAAe,EACpCT,EAAY,SAAUU,EAAc,EAAI,EAAI,EAC5CR,EAAWK,EACX1M,EAAKzB,EAAa,EAEd+N,KAAcA,EAAWQ,EAAU,KACrC7V,GAAYsQ,EAAMZ,GAAgB2F,CAAQ,EAC1CtM,EAAKnB,GAAgByN,CAAQ,GAGnC,CAEA,SAASE,EAAWnU,EAAO,CACzB,IAAI0U,EAAUrQ,EAAQ,QAClBrD,EAAOsK,EAAQtL,EAAQ,QAAU,MAAM,EAC3C,OAAO0U,GAAWzS,GAAKyS,EAAQ1T,CAAI,IAAMnD,GAAS6W,CAAO,EAAI,EAAIA,EAAQ,GAAK,KAChF,CAEA,SAASJ,GAAiB,CACxB,IAAIK,EAAS,GAEb,OAAIZ,IACFY,EAASC,EAAS,EAClBxS,GAAOuS,EAAQ,mCAAmC,EAClDA,EAAS,QAAUA,EAAS,MAAQR,EAAW,EAAK,EAAI,MAAQA,EAAW,EAAI,EAAI,KAG9EQ,CACT,CAEA,SAASC,GAAY,CACnB,OAAO3S,GAAKoC,EAAQ,QAAU9C,GAAKkO,CAAI,EAAE,MAAQpL,EAAQ,WAAW,CACtE,CAEA,SAASkQ,GAAgB,CACvB,OAAOlQ,EAAQ,UAAY,KAAOpC,GAAKoC,EAAQ,UAAU,IAAM0P,EAAW,GAAKc,IACjF,CAEA,SAASL,GAAiB,CACxB,OAAOvS,GAAKoC,EAAQ,WAAW,IAAM0P,EAAW1P,EAAQ,WAAa,KAAOwQ,EAAY,EAAKD,EAAS,EACxG,CAEA,SAASC,GAAe,CACtB,IAAIC,EAAM7S,GAAKoC,EAAQ,GAAG,EAC1B,MAAO,cAAgByQ,GAAO,MAAQA,GAAO,MAAQzQ,EAAQ,SAAW,IAAMyQ,GAAO,MAAQA,GAAO,GACtG,CAEA,SAASC,GAAW,CAClB,OAAOxT,GAAKkO,CAAI,EAAEnE,EAAQ,OAAO,CAAC,CACpC,CAEA,SAAS0J,EAAUxJ,EAAOyJ,EAAY,CACpC,IAAIC,EAAQhC,EAAM1H,GAAS,CAAC,EAC5B,OAAO0J,EAAQ3T,GAAK2T,EAAM,KAAK,EAAE5J,EAAQ,OAAO,CAAC,GAAK2J,EAAa,EAAIE,EAAM,GAAM,CACrF,CAEA,SAASC,EAAU5J,EAAOyJ,EAAY,CACpC,IAAIC,EAAQhC,EAAM1H,CAAK,EAEvB,GAAI0J,EAAO,CACT,IAAIlV,EAAQuB,GAAK2T,EAAM,KAAK,EAAE5J,EAAQ,OAAO,CAAC,EAC1C4G,EAAO3Q,GAAKkO,CAAI,EAAEnE,EAAQ,MAAM,CAAC,EACrC,OAAO3I,EAAI3C,EAAQkS,CAAI,GAAK+C,EAAa,EAAIE,IAC/C,CAEA,MAAO,EACT,CAEA,SAASE,EAAWJ,EAAY,CAC9B,OAAOG,EAAU7N,EAAQ,OAAS,CAAC,EAAI6N,EAAU,CAAC,EAAIJ,EAAU,EAAGC,CAAU,CAC/E,CAEA,SAASE,GAAS,CAChB,IAAID,EAAQhC,EAAM,CAAC,EACnB,OAAOgC,GAAS,WAAWnU,GAAMmU,EAAM,MAAO5J,EAAQ,aAAa,CAAC,CAAC,GAAK,CAC5E,CAEA,SAASgK,EAAWtV,EAAO,CACzB,OAAO,WAAWe,GAAMyO,EAAOlE,EAAQ,WAAatL,EAAQ,QAAU,OAAO,CAAC,CAAC,GAAK,CACtF,CAEA,SAASyU,GAAa,CACpB,OAAOlN,EAAQ,GAAG4I,EAAI,GAAKkF,EAAW,EAAI,EAAIN,EAAQ,CACxD,CAEA,MAAO,CACL,MAAOlF,EACP,OAAQqE,EACR,SAAUa,EACV,UAAWC,EACX,WAAYK,EACZ,UAAWD,EACX,WAAYE,EACZ,WAAYb,CAChB,CACA,CAEA,IAAIc,GAAa,EAEjB,SAASC,GAAOjO,EAASgC,EAAalF,EAAS,CAC7C,IAAIE,EAAQ+C,EAAeC,CAAO,EAC9BG,EAAKnD,EAAM,GACXyK,EAAWzF,EAAY,SACvB+I,EAAS/I,EAAY,OACrB+B,EAAU/B,EAAY,UAAU,QAChCkM,EAAS,CAAA,EACTC,EAEJ,SAAS7F,GAAQ,CACfnI,EAAG3B,EAAe4P,CAAO,EACzBjO,EAAG,CAAC1B,EAAeC,EAAY,EAAG2P,CAAO,GAErCF,EAAaG,OACfC,EAASJ,CAAU,EACnBnM,EAAY,OAAO,OAAO,EAAI,EAElC,CAEA,SAASoM,GAAU,CACjBvQ,EAAO,EACPyK,EAAK,CACP,CAEA,SAASzK,GAAU,CACjB5D,GAAOiU,CAAM,EACb1Y,GAAM0Y,CAAM,EACZlR,EAAM,QAAO,CACf,CAEA,SAASqR,GAAU,CACjB,IAAItN,EAAQuN,EAAiB,EAEzBH,IAAepN,IACboN,EAAapN,GAAS,CAACA,IACzB/D,EAAM,KAAKwB,CAAa,CAG9B,CAEA,SAAS+P,EAASxN,EAAO,CACvB,IAAI+G,EAASiD,EAAO,IAAG,EAAG,MAAK,EAC3BiB,EAASlE,EAAO,OAEpB,GAAIkE,EAAQ,CACV,KAAOlE,EAAO,OAAS/G,GACrB5J,GAAK2Q,EAAQA,CAAM,EAGrB3Q,GAAK2Q,EAAO,MAAM,CAAC/G,CAAK,EAAG+G,EAAO,MAAM,EAAG/G,CAAK,CAAC,EAAE,QAAQ,SAAU4M,EAAO1J,EAAO,CACjF,IAAIuK,EAASvK,EAAQlD,EACjB0N,EAAQC,EAAUf,EAAM,MAAO1J,CAAK,EACxCuK,EAAS1W,GAAO2W,EAAO3G,EAAO,CAAC,EAAE,KAAK,EAAInQ,GAAO8P,EAAS,KAAMgH,CAAK,EACrEtX,GAAK+W,EAAQO,CAAK,EAClB1D,EAAO,SAAS0D,EAAOxK,EAAQlD,GAASyN,EAAS,EAAIxC,GAAS2B,EAAM,KAAK,CAC3E,CAAC,CACH,CACF,CAEA,SAASe,EAAUpX,EAAK2M,EAAO,CAC7B,IAAIwK,EAAQnX,EAAI,UAAU,EAAI,EAC9B,OAAAI,GAAS+W,EAAO3R,EAAQ,QAAQ,KAAK,EACrC2R,EAAM,GAAKzO,EAAQ,KAAK,GAAK,SAAW5D,GAAI6H,EAAQ,CAAC,EAC9CwK,CACT,CAEA,SAASH,GAAoB,CAC3B,IAAIK,EAAU7R,EAAQ,OAEtB,GAAI,CAACkD,EAAQ,GAAG2I,EAAI,EAClBgG,EAAU,UACDhY,GAAYgY,CAAO,EAAG,CAC/B,IAAIC,EAAY9R,EAAQiH,EAAQ,YAAY,CAAC,GAAK/B,EAAY,OAAO,UAAU,CAAC,EAC5E6M,EAAaD,GAAazT,GAAKnB,GAAKyN,EAAS,KAAK,EAAE1D,EAAQ,OAAO,CAAC,EAAI6K,CAAS,EACrFD,EAAUE,GAAc/R,EAAQiH,EAAQ,WAAW,CAAC,GAAK/D,EAAQ,QAAUlD,EAAQ,QAAUkR,EAC/F,CAEA,OAAOW,CACT,CAEA,MAAO,CACL,MAAOrG,EACP,QAASzK,CACb,CACA,CAEA,SAASiR,GAAK9O,EAASgC,EAAalF,EAAS,CAC3C,IAAIiS,EAAmBhP,EAAeC,CAAO,EACzCG,EAAK4O,EAAiB,GACtB3O,EAAO2O,EAAiB,KAExB1N,EAAMrB,EAAQ,MAAM,IACpBgP,EAAsBhN,EAAY,OAClCyL,EAAYuB,EAAoB,UAChCjB,EAAaiB,EAAoB,WACjCnB,EAAYmB,EAAoB,UAChCxB,EAAWwB,EAAoB,SAC/BlB,EAAakB,EAAoB,WACjCC,EAAwBjN,EAAY,UACpC+B,EAAUkL,EAAsB,QAChC7K,EAAS6K,EAAsB,OAC/BC,EAAyBlN,EAAY,SACrCkG,EAAOgH,EAAuB,KAC9BjH,EAAQiH,EAAuB,MAC/BC,EAEJ,SAAS7G,GAAQ,CACf6G,EAAanN,EAAY,WACzB7B,EAAG,CAACpC,GAAeY,GAAeF,EAAeD,CAAa,EAAG4Q,CAAU,CAC7E,CAEA,SAASA,GAAa,CACfpN,EAAY,WAAW,WAC1BA,EAAY,OAAO,OAAM,EACzBqN,EAAKrP,EAAQ,KAAK,EAClBgC,EAAY,OAAO,OAAM,EAE7B,CAEA,SAASsN,EAAKC,EAAMtL,EAAOuL,EAAM3S,EAAU,CACrC0S,IAAStL,GAASwL,EAASF,EAAOC,CAAI,IACxCrO,EAAM,EACNuO,EAAUC,EAAMC,EAAW,EAAIL,EAAOC,CAAI,EAAG,EAAI,GAGnDnO,EAAIlM,EAAM,EACViL,EAAKnC,GAAYgG,EAAOuL,EAAMD,CAAI,EAClCJ,EAAW,MAAMlL,EAAO,UAAY,CAClC5C,EAAInM,EAAI,EACRkL,EAAKlC,GAAa+F,EAAOuL,EAAMD,CAAI,EACnC1S,GAAYA,EAAQ,CACtB,CAAC,CACH,CAEA,SAASwS,EAAKpL,EAAO,CACnByL,EAAUG,EAAW5L,EAAO,EAAI,CAAC,CACnC,CAEA,SAASyL,EAAUI,EAAUC,EAAa,CACxC,GAAI,CAAC/P,EAAQ,GAAG4I,EAAI,EAAG,CACrB,IAAIoH,EAAcD,EAAcD,EAAWG,EAAKH,CAAQ,EACxDtW,GAAM0O,EAAM,YAAa,YAAcnE,EAAQ,GAAG,EAAI,IAAMiM,EAAc,KAAK,EAC/EF,IAAaE,GAAe5P,EAAKP,EAAa,CAChD,CACF,CAEA,SAASoQ,EAAKH,EAAU,CACtB,GAAI9P,EAAQ,GAAG2I,EAAI,EAAG,CACpB,IAAI1E,EAAQiM,EAAQJ,CAAQ,EACxBK,EAAclM,EAAQjC,EAAY,WAAW,OAAM,EACnDoO,EAAcnM,EAAQ,GAEtBmM,GAAeD,KACjBL,EAAWH,EAAMG,EAAUK,CAAW,EAE1C,CAEA,OAAOL,CACT,CAEA,SAASH,EAAMG,EAAUO,EAAW,CAClC,IAAIC,EAASR,EAAWS,EAASF,CAAS,EACtCG,EAAO1C,EAAU,EACrB,OAAAgC,GAAY1L,EAAOoM,GAAQrV,GAAKC,EAAIkV,CAAM,EAAIE,CAAI,GAAK,EAAE,GAAKH,EAAY,EAAI,IACvEP,CACT,CAEA,SAAS3O,GAAS,CAChBuO,EAAUE,EAAW,EAAI,EAAI,EAC7BT,EAAW,OAAM,CACnB,CAEA,SAASe,EAAQJ,EAAU,CAKzB,QAJI/E,EAAS/I,EAAY,OAAO,IAAG,EAC/BiC,EAAQ,EACRwM,EAAc,IAEThc,EAAI,EAAGA,EAAIsW,EAAO,OAAQtW,IAAK,CACtC,IAAIqU,GAAaiC,EAAOtW,CAAC,EAAE,MACvBoW,EAAWzP,EAAIyU,EAAW/G,GAAY,EAAI,EAAIgH,CAAQ,EAE1D,GAAIjF,GAAY4F,EACdA,EAAc5F,EACd5G,EAAQ6E,OAER,MAEJ,CAEA,OAAO7E,CACT,CAEA,SAAS4L,EAAW5L,EAAOyM,EAAU,CACnC,IAAIZ,EAAW1L,EAAOyJ,EAAU5J,EAAQ,CAAC,EAAIE,EAAOF,CAAK,CAAC,EAC1D,OAAOyM,EAAWC,EAAKb,CAAQ,EAAIA,CACrC,CAEA,SAASF,GAAc,CACrB,IAAIjF,EAAO5G,EAAQ,MAAM,EACzB,OAAO/J,GAAKkO,CAAI,EAAEyC,CAAI,EAAI3Q,GAAKiO,CAAK,EAAE0C,CAAI,EAAIvG,EAAO2J,EAAW,EAAK,CAAC,CACxE,CAEA,SAAS4C,EAAKb,EAAU,CACtB,OAAIhT,EAAQ,WAAakD,EAAQ,GAAG0I,EAAK,IACvCoH,EAAWhU,GAAMgU,EAAU,EAAG1L,EAAO0J,EAAW,EAAI,EAAIN,EAAQ,CAAE,CAAC,GAG9DsC,CACT,CAEA,SAAS3L,EAAOF,EAAO,CACrB,IAAIrK,EAAQkD,EAAQ,MACpB,OAAOlD,IAAU,UAAY4T,EAAQ,EAAKC,EAAUxJ,EAAO,EAAI,GAAK,EAAI,CAACrK,EAAQ6T,EAAUxJ,CAAK,GAAK,CACvG,CAEA,SAASsM,EAAStV,EAAK,CACrB,OAAO4U,EAAW5U,EAAM+G,EAAY,WAAW,OAAM,EAAK,EAAG,CAAC,CAAClF,EAAQ,SAAS,CAClF,CAEA,SAAS2S,EAASY,EAAW,CAC3B,IAAIO,EAAUxM,EAAOuL,EAAMC,EAAW,EAAIS,CAAS,CAAC,EACpD,OAAOA,EAAYO,GAAW,EAAIA,GAAW1I,EAAKnE,EAAQ,aAAa,CAAC,EAAI/J,GAAKiO,CAAK,EAAElE,EAAQ,OAAO,CAAC,CAC1G,CAEA,SAAS8M,EAAc5V,EAAK6U,EAAU,CACpCA,EAAWnZ,GAAYmZ,CAAQ,EAAIF,EAAW,EAAKE,EACnD,IAAIM,EAAcnV,IAAQ,IAAQmJ,EAAO0L,CAAQ,EAAI1L,EAAOmM,EAAS,EAAK,CAAC,EACvEJ,EAAclV,IAAQ,IAASmJ,EAAO0L,CAAQ,EAAI1L,EAAOmM,EAAS,EAAI,CAAC,EAC3E,OAAOH,GAAeD,CACxB,CAEA,MAAO,CACL,MAAO7H,EACP,KAAMgH,EACN,KAAMD,EACN,UAAWK,EACX,MAAOC,EACP,OAAQxO,EACR,QAAS+O,EACT,WAAYL,EACZ,YAAaD,EACb,SAAUW,EACV,cAAeM,EACf,WAAYzB,CAChB,CACA,CAEA,SAAS1D,GAAW1L,EAASgC,EAAalF,EAAS,CACjD,IAAIgU,EAAmB/Q,EAAeC,CAAO,EACzCG,EAAK2Q,EAAiB,GACtB1Q,EAAO0Q,EAAiB,KAExBhC,EAAO9M,EAAY,KACnB4N,EAAcd,EAAK,YACnByB,EAAWzB,EAAK,SAChBe,EAAaf,EAAK,WAClBiC,EAAsB/O,EAAY,OAClCmK,EAAW4E,EAAoB,SAC/B7E,EAAY6E,EAAoB,UAChCC,EAAUlU,EAAQ,QAClBmU,EAASjR,EAAQ,GAAG2I,EAAI,EACxBuI,EAAUlR,EAAQ,GAAG0I,EAAK,EAC1ByI,EAAUrb,EAAMsb,EAAa,EAAK,EAClCC,EAAUvb,EAAMsb,EAAa,EAAI,EACjCE,EAAYxU,EAAQ,OAAS,EAC7ByU,EACAC,EAAYF,EACZG,EACAC,EACAC,EAEJ,SAASrJ,GAAQ,CACfD,EAAI,EACJlI,EAAG,CAAC1B,EAAeD,EAAesB,EAAuB,EAAGuI,CAAI,EAChElI,EAAGxB,GAAeiT,CAAS,CAC7B,CAEA,SAASvJ,GAAO,CACdoJ,EAAavF,EAAU,EAAI,EAC3BwF,EAAU5U,EAAQ,QAClB6U,EAAU7U,EAAQ,QAClByU,EAAWM,EAAM,EACjB,IAAI5N,EAAQnI,GAAMwV,EAAW,EAAGN,EAAUO,EAAWE,EAAa,CAAC,EAE/DxN,IAAUqN,IACZA,EAAYrN,EACZ6K,EAAK,WAAU,EAEnB,CAEA,SAAS8C,GAAY,CACfL,IAAaM,KACfzR,EAAKN,EAAuB,CAEhC,CAEA,SAASgS,EAAGC,EAASC,EAAgBnV,EAAU,CAC7C,GAAI,CAACoV,GAAM,EAAI,CACb,IAAI1C,EAAO2C,EAAMH,CAAO,EACpB9N,EAAQgM,EAAKV,CAAI,EAEjBtL,EAAQ,KAAO+N,GAAkB/N,IAAUqN,KAC7Ca,EAASlO,CAAK,EACd6K,EAAK,KAAKS,EAAMtL,EAAOuN,EAAW3U,CAAQ,EAE9C,CACF,CAEA,SAASuV,EAAOpC,EAAalO,EAAUuQ,EAAMxV,EAAU,CACrDmF,EAAY,OAAO,OAAOgO,EAAalO,EAAUuQ,EAAM,UAAY,CACjE,IAAIpO,EAAQgM,EAAKnB,EAAK,QAAQc,EAAW,CAAE,CAAC,EAC5CuC,EAASnB,EAAUhW,GAAIiJ,EAAOsN,CAAQ,EAAItN,CAAK,EAC/CpH,GAAYA,EAAQ,CACtB,CAAC,CACH,CAEA,SAASqV,EAAMH,EAAS,CACtB,IAAI9N,EAAQqN,EAEZ,GAAI5a,GAASqb,CAAO,EAAG,CACrB,IAAIO,EAAOP,EAAQ,MAAM,iBAAiB,GAAK,CAAA,EAC3CQ,EAAYD,EAAK,CAAC,EAClB5W,EAAS4W,EAAK,CAAC,EAEfC,IAAc,KAAOA,IAAc,IACrCtO,EAAQuO,EAAiBlB,GAAY,EAAE,GAAKiB,GAAa,CAAC7W,GAAU,IAAK4V,CAAS,EACzEiB,IAAc,IACvBtO,EAAQvI,EAASwU,EAAQ,CAACxU,CAAM,EAAIyV,EAAQ,EAAI,EACvCoB,IAAc,MACvBtO,EAAQoN,EAAQ,EAAI,EAExB,MACEpN,EAAQgN,EAASc,EAAUjW,GAAMiW,EAAS,EAAGR,CAAQ,EAGvD,OAAOtN,CACT,CAEA,SAASmN,EAAY5B,EAAMQ,EAAa,CACtC,IAAItU,EAASgW,IAAYe,EAAQ,EAAK,EAAId,GACtCpC,EAAOiD,EAAiBlB,EAAY5V,GAAU8T,EAAO,GAAK,GAAI8B,EAAW,EAAEI,GAAWe,EAAQ,EAAG,EAErG,OAAIlD,IAAS,IAAM2B,GACb,CAAC7V,GAAmBuU,EAAW,EAAIW,EAAS,CAACf,CAAI,EAAG,CAAC,EAChDA,EAAO,EAAI+B,EAIfvB,EAAcT,EAAOU,EAAKV,CAAI,CACvC,CAEA,SAASiD,EAAiBjD,EAAMpI,EAAMuL,EAAU,CAC9C,GAAIvG,EAAQ,GAAMsG,IAAY,CAC5B,IAAIxO,EAAQ0O,EAAwBpD,CAAI,EAEpCtL,IAAUsL,IACZpI,EAAOoI,EACPA,EAAOtL,EACPyO,EAAW,IAGTnD,EAAO,GAAKA,EAAOgC,EACjB,CAACG,IAAYjW,GAAQ,EAAG8T,EAAMpI,EAAM,EAAI,GAAK1L,GAAQ8V,EAAUpK,EAAMoI,EAAM,EAAI,GACjFA,EAAOW,EAAQ0C,EAAOrD,CAAI,CAAC,EAEvB0B,EACF1B,EAAOmD,EAAWnD,EAAO,EAAI,EAAEkC,EAAaE,GAAWA,GAAWF,EAAalC,EACtEzS,EAAQ,OACjByS,EAAOA,EAAO,EAAIgC,EAAW,EAE7BhC,EAAO,GAIPmD,GAAYnD,IAASpI,IACvBoI,EAAOW,EAAQ0C,EAAOzL,CAAI,GAAKoI,EAAOpI,EAAO,GAAK,EAAE,EAG1D,MACEoI,EAAO,GAGT,OAAOA,CACT,CAEA,SAASoD,EAAwBpD,EAAM,CACrC,GAAI2B,GAAWpU,EAAQ,YAAc,QAAUyS,IAAS+B,EAGtD,QAFIxB,EAAWF,EAAW,EAEnBE,IAAaD,EAAWN,EAAM,EAAI,GAAK9T,GAAQ8T,EAAM,EAAGvP,EAAQ,OAAS,EAAG,CAAClD,EAAQ,MAAM,GAChGyS,EAAO+B,EAAY,EAAE/B,EAAO,EAAEA,EAIlC,OAAOA,CACT,CAEA,SAASU,EAAKhM,EAAO,CACnB,OAAOgN,GAAUhN,EAAQwN,GAAcA,GAAc,EAAIxN,CAC3D,CAEA,SAAS4N,GAAS,CAGhB,QAFIhc,EAAM4b,GAAcgB,EAAQ,GAAMxB,GAAUS,EAAU,EAAIC,GAEvDX,GAAWnb,KAAQ,GACxB,GAAIga,EAAW4B,EAAa,EAAG,EAAI,IAAM5B,EAAWha,EAAK,EAAI,EAAG,CAC9DA,IACA,KACF,CAGF,OAAOiG,GAAMjG,EAAK,EAAG4b,EAAa,CAAC,CACrC,CAEA,SAASvB,EAAQzE,EAAM,CACrB,OAAO3P,GAAM2W,IAAahH,EAAOkG,EAAUlG,EAAM,EAAG8F,CAAQ,CAC9D,CAEA,SAASqB,EAAO3O,EAAO,CACrB,OAAOwO,EAAQ,EAAKzX,GAAIiJ,EAAOsN,CAAQ,EAAIrW,IAAO+I,GAASsN,EAAWE,EAAa,EAAIxN,GAAS0N,CAAO,CACzG,CAEA,SAASkB,EAAO7C,EAAa,CAC3B,IAAI9I,EAAU4H,EAAK,QAAQkB,CAAW,EACtC,OAAOkB,EAAUpV,GAAMoL,EAAS,EAAGqK,CAAQ,EAAIrK,CACjD,CAEA,SAASiL,EAASlO,EAAO,CACnBA,IAAUqN,IACZE,EAAYF,EACZA,EAAYrN,EAEhB,CAEA,SAAS6O,EAAStD,EAAM,CACtB,OAAOA,EAAOgC,EAAYF,CAC5B,CAEA,SAASmB,GAAW,CAClB,MAAO,CAAC9b,GAAYmG,EAAQ,KAAK,GAAKA,EAAQ,YAChD,CAEA,SAASmV,IAAS,CAChB,OAAOjS,EAAQ,MAAM,GAAG,CAAC7K,GAAQC,EAAS,CAAC,GAAK,CAAC,CAAC0H,EAAQ,iBAC5D,CAEA,MAAO,CACL,MAAOwL,EACP,GAAIwJ,EACJ,OAAQM,EACR,QAASjB,EACT,QAASE,EACT,YAAaD,EACb,OAAQS,EACR,SAAUM,EACV,SAAUW,EACV,QAAS5C,EACT,OAAQ0C,EACR,OAAQC,EACR,SAAUJ,EACV,OAAQR,EACZ,CACA,CAEA,IAAIc,GAAiB,6BACjBC,GAAO,wFACPC,GAAO,GAEX,SAASC,GAAOlT,EAASgC,EAAalF,EAAS,CAC7C,IAAIE,EAAQ+C,EAAeC,CAAO,EAC9BG,EAAKnD,EAAM,GACXN,EAAOM,EAAM,KACboD,EAAOpD,EAAM,KACbzF,EAAUuF,EAAQ,QAClB8K,EAAO9K,EAAQ,KACf2K,EAAWzF,EAAY,SACvB0J,EAAa1J,EAAY,WACzBmR,EAAc1L,EAAS,OACvBQ,EAAQR,EAAS,MACjB2L,EAAUD,EACV3D,EAAO/H,EAAS,KAChB4L,EAAO5L,EAAS,KAChB6L,EACAC,EACAC,EAAS,CAAA,EAEb,SAASlL,GAAQ,CACfD,EAAI,EACJlI,EAAG1B,EAAe2P,CAAO,CAC3B,CAEA,SAASA,GAAU,CACjBvQ,EAAO,EACPyK,EAAK,CACP,CAEA,SAASD,GAAO,CACd,IAAIoL,EAAU3W,EAAQ,OAElB2W,GAAW,EAAEjE,GAAQ6D,IACvBK,EAAY,EAGVlE,GAAQ6D,IACV1a,GAAO6a,EAAQ,CACb,KAAMhE,EACN,KAAM6D,CACd,CAAO,EACD3Z,GAAQ0Z,EAASK,EAAU,GAAK,MAAM,EACtC/b,GAAS0b,EAASG,EAAiB1N,GAAe,KAAO/I,EAAQ,SAAS,EAEtE2W,IACFhK,EAAM,EACNzI,EAAM,EACN5H,EAAa,CAACoW,EAAM6D,CAAI,EAAG5O,GAAewD,EAAM,EAAE,EAClD7H,EAAKjB,GAAsBqQ,EAAM6D,CAAI,GAG3C,CAEA,SAASxV,GAAU,CACjBb,EAAM,QAAO,EACbxC,GAAY4Y,EAASG,CAAc,EAE/BD,GACFrZ,GAAOkZ,EAAc,CAAC3D,EAAM6D,CAAI,EAAID,CAAO,EAC3C5D,EAAO6D,EAAO,MAEdra,GAAgB,CAACwW,EAAM6D,CAAI,EAAGjO,EAAc,CAEhD,CAEA,SAASqE,GAAS,CAChBtJ,EAAG,CAACpC,GAAeG,GAAaM,EAAeQ,GAAgBc,EAAuB,EAAGkB,CAAM,EAC/FtE,EAAK2W,EAAM,QAASvd,EAAMgc,EAAI,GAAG,CAAC,EAClCpV,EAAK8S,EAAM,QAAS1Z,EAAMgc,EAAI,GAAG,CAAC,CACpC,CAEA,SAASA,EAAGC,EAAS,CACnBrG,EAAW,GAAGqG,EAAS,EAAI,CAC7B,CAEA,SAAS2B,GAAe,CACtBN,EAAUD,GAAe7Z,GAAO,MAAO/B,EAAQ,MAAM,EACrDiY,EAAOmE,EAAY,EAAI,EACvBN,EAAOM,EAAY,EAAK,EACxBL,EAAU,GACV3b,GAAOyb,EAAS,CAAC5D,EAAM6D,CAAI,CAAC,EAC5B,CAACF,GAAerb,GAAOsb,EAASnL,CAAK,CACvC,CAEA,SAAS0L,EAAYC,EAAO,CAC1B,IAAIC,EAAQ,kBAAqBtc,EAAQ,MAAQ,KAAOqc,EAAQrc,EAAQ,KAAOA,EAAQ,MAAQ,+BAAqCwb,GAAiB,kBAAsBE,GAAO,IAAMA,GAAO,YAAgBA,GAAO,aAAiBA,GAAO,iCAAuCnW,EAAQ,WAAakW,IAAQ,OAClT,OAAO9Y,GAAU2Z,CAAK,CACxB,CAEA,SAAS7S,GAAS,CAChB,GAAIwO,GAAQ6D,EAAM,CAChB,IAAIpP,EAAQjE,EAAQ,MAChBwR,EAAY9F,EAAW,QAAO,EAC9BoI,EAAYpI,EAAW,QAAO,EAC9BqI,EAAYvC,EAAY,IAAMvN,EAAQuN,EAAY5J,EAAK,KAAOA,EAAK,KACnEoM,EAAYF,EAAY,IAAM7P,EAAQ6P,EAAYlM,EAAK,MAAQA,EAAK,KACxE4H,EAAK,SAAWgC,EAAY,EAC5B6B,EAAK,SAAWS,EAAY,EAC5B1a,EAAaoW,EAAM5K,GAAYmP,CAAS,EACxC3a,EAAaia,EAAMzO,GAAYoP,CAAS,EACxC5T,EAAKhB,GAAsBoQ,EAAM6D,EAAM7B,EAAWsC,CAAS,CAC7D,CACF,CAEA,MAAO,CACL,OAAQN,EACR,MAAOlL,EACP,QAASzK,EACT,OAAQmD,CACZ,CACA,CAEA,IAAIiT,GAA0BrZ,GAAiB,YAE/C,SAASsZ,GAASlU,EAASgC,EAAalF,EAAS,CAC/C,IAAIqX,EAAmBpU,EAAeC,CAAO,EACzCG,EAAKgU,EAAiB,GACtBzX,EAAOyX,EAAiB,KACxB/T,EAAO+T,EAAiB,KAExB7T,EAAWD,GAAgBvD,EAAQ,SAAUkD,EAAQ,GAAG,KAAKA,EAAS,GAAG,EAAGoU,CAAgB,EAC5F7S,EAAWjB,EAAS,SACpBmH,EAAWzF,EAAY,SACvBqS,EAAyBrS,EAAY,SACrC2F,EAAO0M,EAAuB,KAC9BC,EAASD,EAAuB,OAChCE,EAAWzX,EAAQ,SACnB0X,EACAC,EACAC,EAAUH,IAAa,QAE3B,SAASjM,GAAQ,CACXiM,IACF9K,EAAM,EACN6K,GAAUlb,EAAakb,EAAQ7P,GAAegD,EAAS,MAAM,EAAE,EAC/DiN,GAAWC,EAAI,EACf3T,EAAM,EAEV,CAEA,SAASyI,GAAS,CACZ3M,EAAQ,cACVJ,EAAKiL,EAAM,wBAAyB,SAAUnK,EAAG,CAC/CgX,EAAUhX,EAAE,OAAS,aACrBoX,EAAU,CACZ,CAAC,EAGC9X,EAAQ,cACVJ,EAAKiL,EAAM,mBAAoB,SAAUnK,EAAG,CAC1CiX,EAAUjX,EAAE,OAAS,UACrBoX,EAAU,CACZ,CAAC,EAGCN,GACF5X,EAAK4X,EAAQ,QAAS,UAAY,CAChCI,EAAUC,EAAI,EAAK1T,EAAM,EAAI,CAC/B,CAAC,EAGHd,EAAG,CAAClC,GAAYc,GAAcP,CAAa,EAAG8B,EAAS,MAAM,EAC7DH,EAAGlC,GAAY2L,CAAM,CACvB,CAEA,SAAS+K,GAAO,CACVpT,EAAQ,GAAMS,EAAY,OAAO,SAAQ,IAC3C1B,EAAS,MAAM,CAACxD,EAAQ,aAAa,EACrC2X,EAAUD,EAAUE,EAAU,GAC9B1T,EAAM,EACNZ,EAAKZ,EAAmB,EAE5B,CAEA,SAASyB,EAAM4T,EAAM,CACfA,IAAS,SACXA,EAAO,IAGTH,EAAU,CAAC,CAACG,EACZ7T,EAAM,EAEDO,EAAQ,IACXjB,EAAS,MAAK,EACdF,EAAKV,EAAoB,EAE7B,CAEA,SAASkV,GAAa,CACfF,IACHF,GAAWC,EAAUxT,EAAM,EAAK,EAAI0T,EAAI,EAE5C,CAEA,SAAS3T,GAAS,CACZsT,IACFjd,GAAYid,EAAQ7N,GAAc,CAACiO,CAAO,EAC1Ctb,EAAakb,EAAQ1P,GAAY9H,EAAQ,KAAK4X,EAAU,OAAS,OAAO,CAAC,EAE7E,CAEA,SAASN,EAAiBxT,EAAM,CAC9B,IAAIkU,EAAMrN,EAAS,IACnBqN,GAAOtb,GAAMsb,EAAK,QAASlU,EAAO,IAAM,GAAG,EAC3CR,EAAKX,GAAwBmB,CAAI,CACnC,CAEA,SAASgJ,EAAO3F,EAAO,CACrB,IAAI0J,EAAQ3L,EAAY,OAAO,MAAMiC,CAAK,EAC1C3D,EAAS,IAAIqN,GAAS,CAAC9T,GAAa8T,EAAM,MAAOsG,EAAuB,GAAKnX,EAAQ,QAAQ,CAC/F,CAEA,MAAO,CACL,MAAOwL,EACP,QAAShI,EAAS,OAClB,KAAMqU,EACN,MAAO1T,EACP,SAAUM,CACd,CACA,CAEA,SAASwT,GAAM/U,EAASgC,EAAalF,EAAS,CAC5C,IAAIkY,EAAmBjV,EAAeC,CAAO,EACzCG,EAAK6U,EAAiB,GAE1B,SAAS1M,GAAQ,CACXxL,EAAQ,QACVqD,EAAGR,GAAuB7J,EAAMwe,EAAQ,EAAI,CAAC,EAC7CnU,EAAG,CAACpC,GAAeU,EAAeD,CAAa,EAAG1I,EAAMmf,EAAO,EAAI,CAAC,EAExE,CAEA,SAASA,EAAMC,EAAQ,CACrBlT,EAAY,OAAO,QAAQ,SAAU2L,EAAO,CAC1C,IAAI1B,EAAM5T,GAAMsV,EAAM,WAAaA,EAAM,MAAO,KAAK,EAEjD1B,GAAOA,EAAI,KACbqI,EAAOY,EAAQjJ,EAAK0B,CAAK,CAE7B,CAAC,CACH,CAEA,SAAS2G,EAAOY,EAAQjJ,EAAK0B,EAAO,CAClCA,EAAM,MAAM,aAAcuH,EAAS,+BAAkCjJ,EAAI,IAAM,KAAQ,GAAI,EAAI,EAC/FvS,GAAQuS,EAAKiJ,EAAS,OAAS,EAAE,CACnC,CAEA,MAAO,CACL,MAAO5M,EACP,QAASxS,EAAMmf,EAAO,EAAK,CAC/B,CACA,CAEA,IAAIE,GAAwB,GACxBC,GAAkB,IAClBC,GAAkB,GAClBC,GAAgB,IAChBC,GAAe,IAEnB,SAASC,GAAOxV,EAASgC,EAAalF,EAAS,CAC7C,IAAI2Y,EAAmB1V,EAAeC,CAAO,EACzCG,EAAKsV,EAAiB,GACtBrV,EAAOqV,EAAiB,KAExBpU,EAAMrB,EAAQ,MAAM,IACpB8O,EAAO9M,EAAY,KACnB4N,EAAcd,EAAK,YACnByB,EAAWzB,EAAK,SAChB+B,EAAgB/B,EAAK,cACrBY,EAAYZ,EAAK,UACjBoC,EAAUlR,EAAQ,GAAG0I,EAAK,EAC1BpI,EACAzD,EACA6Y,EAAW,EAEf,SAASpN,GAAQ,CACfnI,EAAGlC,GAAY0X,CAAK,EACpBxV,EAAG,CAAC1B,EAAeD,CAAa,EAAG2C,CAAM,CAC3C,CAEA,SAASiR,EAAOpC,EAAalO,EAAUuQ,EAAMuD,EAAYC,EAAa,CACpE,IAAI1O,EAAOyI,EAAW,EAGtB,GAFA+F,EAAK,EAEDtD,IAAS,CAACnB,GAAW,CAACL,EAAa,GAAK,CAC1C,IAAIL,EAAOxO,EAAY,OAAO,WAAU,EACpCmC,EAASpI,GAAKiU,CAAW,EAAIQ,EAAOtV,GAAME,EAAI4U,CAAW,EAAIQ,CAAI,GAAK,EAC1ER,EAAclB,EAAK,WAAW9M,EAAY,WAAW,OAAOgO,EAAcQ,CAAI,CAAC,EAAIrM,CACrF,CAEA,IAAI2R,EAAaza,GAAmB8L,EAAM6I,EAAa,CAAC,EACxD0F,EAAW,EACX5T,EAAWgU,EAAa,EAAIhU,GAAY7G,GAAIG,EAAI4U,EAAc7I,CAAI,EAAImO,GAAeC,EAAY,EACjG1Y,EAAW+Y,EACXtV,EAAWD,GAAgByB,EAAUiU,EAAOjgB,EAAMkL,EAAQmG,EAAM6I,EAAa6F,CAAW,EAAG,CAAC,EAC5FxU,EAAIjM,EAAS,EACbgL,EAAKrB,EAAY,EACjBuB,EAAS,MAAK,CAChB,CAEA,SAASyV,GAAQ,CACf1U,EAAInM,EAAI,EACR2H,GAAYA,EAAQ,EACpBuD,EAAKpB,EAAc,CACrB,CAEA,SAASgC,EAAOmG,EAAM6O,EAAIH,EAAajV,EAAM,CAC3C,IAAIkP,EAAWF,EAAW,EACtBrb,EAAS4S,GAAQ6O,EAAK7O,GAAQ8O,EAAOrV,CAAI,EACzCkK,GAAQvW,EAASub,GAAY4F,EACjChG,EAAUI,EAAWhF,CAAI,EAErBoG,GAAW,CAAC2E,GAAehF,MAC7B6E,GAAYL,GAERja,EAAI0P,CAAI,EAAIqK,IACd/C,EAAO7B,EAASM,EAAc,EAAI,CAAC,EAAGuE,GAAiB,GAAOvY,EAAU,EAAI,EAGlF,CAEA,SAAS8Y,GAAQ,CACXrV,GACFA,EAAS,OAAM,CAEnB,CAEA,SAASa,GAAS,CACZb,GAAY,CAACA,EAAS,aACxBqV,EAAK,EACLI,EAAK,EAET,CAEA,SAASE,EAAOC,EAAG,CACjB,IAAIC,EAAarZ,EAAQ,WACzB,OAAOqZ,EAAaA,EAAWD,CAAC,EAAI,EAAI,KAAK,IAAI,EAAIA,EAAG,CAAC,CAC3D,CAEA,MAAO,CACL,MAAO5N,EACP,QAASqN,EACT,OAAQvD,EACR,OAAQjR,CACZ,CACA,CAEA,IAAIiV,GAA0B,CAC5B,QAAS,GACT,QAAS,EACX,EAEA,SAASC,GAAKrW,EAASgC,EAAalF,EAAS,CAC3C,IAAIwZ,EAAmBvW,EAAeC,CAAO,EACzCG,EAAKmW,EAAiB,GACtBlW,EAAOkW,EAAiB,KACxB5Z,EAAO4Z,EAAiB,KACxBlZ,EAASkZ,EAAiB,OAE1B5U,EAAQ1B,EAAQ,MAChB8O,EAAO9M,EAAY,KACnBwT,EAASxT,EAAY,OACrB0J,EAAa1J,EAAY,WACzBiG,EAAQjG,EAAY,SAAS,MAC7BiB,EAASjB,EAAY,MAAM,OAC3BuU,EAAyBvU,EAAY,UACrC+B,EAAUwS,EAAuB,QACjCnS,EAASmS,EAAuB,OAChC3G,EAAcd,EAAK,YACnB+B,EAAgB/B,EAAK,cACrB0H,EACAC,EACAC,EACAC,EACAC,EACAC,EAAW,GACXC,EACAC,EACAxiB,EAEJ,SAAS+T,GAAQ,CACf5L,EAAKuL,EAAOV,GAAqBtR,GAAMmgB,EAAuB,EAC9D1Z,EAAKuL,EAAOT,GAAmBvR,GAAMmgB,EAAuB,EAC5D1Z,EAAKuL,EAAOX,GAAqB0P,EAAeZ,EAAuB,EACvE1Z,EAAKuL,EAAO,QAASgP,EAAS,CAC5B,QAAS,EACf,CAAK,EACDva,EAAKuL,EAAO,YAAa7N,EAAO,EAChC+F,EAAG,CAACpC,GAAeU,CAAa,EAAG4J,CAAI,CACzC,CAEA,SAASA,GAAO,CACd,IAAI6O,EAAOpa,EAAQ,KACnBqa,GAAQ,CAACD,CAAI,EACbP,EAASO,IAAS,MACpB,CAEA,SAASF,EAAcxZ,EAAG,CAGxB,GAFAsZ,EAAiB,GAEb,CAACC,EAAU,CACb,IAAIK,EAAUC,EAAa7Z,CAAC,EAExB8Z,EAAY9Z,EAAE,MAAM,IAAM4Z,GAAW,CAAC5Z,EAAE,UACrCkO,EAAW,SAUdtR,GAAQoD,EAAG,EAAI,GATfjJ,EAAS6iB,EAAUnP,EAAQ,OAC3B2O,EAAWlV,EAAM,GAAG,CAACvM,GAAQC,EAAS,CAAC,EACvCshB,EAAgB,KAChBha,EAAKnI,EAAQgT,GAAqBgQ,EAAenB,EAAuB,EACxE1Z,EAAKnI,EAAQiT,GAAmBgQ,EAAapB,EAAuB,EACpEtH,EAAK,OAAM,EACX0G,EAAO,OAAM,EACbiC,EAAKja,CAAC,GAKZ,CACF,CAEA,SAAS+Z,EAAc/Z,EAAG,CAMxB,GALKkE,EAAM,GAAGrM,EAAQ,IACpBqM,EAAM,IAAIrM,EAAQ,EAClB+K,EAAKxB,EAAU,GAGbpB,EAAE,WACJ,GAAIoZ,EAAU,CACZ9H,EAAK,UAAU0H,EAAekB,EAAUC,EAAUna,CAAC,CAAC,CAAC,EACrD,IAAIoa,EAAUC,GAASra,CAAC,EAAI6J,GACxByQ,GAAcjB,KAAcA,EAAWhG,EAAa,IAEpD+G,GAAWE,KACbL,EAAKja,CAAC,EAGRsZ,EAAiB,GACjB1W,EAAKvB,EAAc,EACnBzE,GAAQoD,CAAC,CACX,MAAWua,EAAkBva,CAAC,IAC5BoZ,EAAWoB,EAAYxa,CAAC,EACxBpD,GAAQoD,CAAC,EAGf,CAEA,SAASga,EAAYha,EAAG,CAClBkE,EAAM,GAAGrM,EAAQ,IACnBqM,EAAM,IAAIxM,EAAI,EACdkL,EAAKtB,EAAa,GAGhB8X,IACFtH,EAAK9R,CAAC,EACNpD,GAAQoD,CAAC,GAGXJ,EAAO7I,EAAQgT,GAAqBgQ,CAAa,EACjDna,EAAO7I,EAAQiT,GAAmBgQ,CAAW,EAC7CZ,EAAW,EACb,CAEA,SAASK,EAAQzZ,EAAG,CACd,CAACuZ,GAAYD,GACf1c,GAAQoD,EAAG,EAAI,CAEnB,CAEA,SAASia,EAAKja,EAAG,CACfkZ,EAAgBD,EAChBA,EAAYjZ,EACZgZ,EAAe5G,EAAW,CAC5B,CAEA,SAASN,EAAK9R,EAAG,CACf,IAAIya,EAAWC,EAAgB1a,CAAC,EAC5BwS,GAAcmI,EAAmBF,CAAQ,EACzC7W,GAAStE,EAAQ,QAAUA,EAAQ,aACvCmG,EAAO,EAAK,EAER0T,EACFjL,EAAW,OAAOsE,GAAa,EAAGlT,EAAQ,IAAI,EACrCkD,EAAQ,GAAG4I,EAAI,EACxB8C,EAAW,GAAGtH,EAAOrI,GAAKkc,CAAQ,CAAC,EAAI,EAAI7W,GAAS,IAAM,IAAMA,GAAS,IAAM,GAAG,EACzEpB,EAAQ,GAAG0I,EAAK,GAAKmO,GAAYzV,GAC1CsK,EAAW,GAAGmF,EAAc,EAAI,EAAI,IAAM,GAAG,EAE7CnF,EAAW,GAAGA,EAAW,OAAOsE,EAAW,EAAG,EAAI,EAGpD/M,EAAO,EAAI,CACb,CAEA,SAAS+U,EAAYxa,EAAG,CACtB,IAAI4a,EAAatb,EAAQ,iBACrBub,GAAQ/hB,GAAS8hB,CAAU,EAC3BE,GAAQD,IAASD,EAAW,OAAS,EACrCG,IAASF,GAAQD,EAAW,MAAQ,CAACA,IAAe,GACxD,OAAOhd,EAAIuc,EAAUna,CAAC,CAAC,GAAK6Z,EAAa7Z,CAAC,EAAI+a,GAAQD,GACxD,CAEA,SAASP,EAAkBva,EAAG,CAC5B,OAAOpC,EAAIuc,EAAUna,CAAC,CAAC,EAAIpC,EAAIuc,EAAUna,EAAG,EAAI,CAAC,CACnD,CAEA,SAAS0a,EAAgB1a,EAAG,CAC1B,GAAIwC,EAAQ,GAAG2I,EAAI,GAAK,CAACkO,EAAU,CACjC,IAAIvV,EAAOuW,GAASra,CAAC,EAErB,GAAI8D,GAAQA,EAAO+F,GACjB,OAAOsQ,EAAUna,CAAC,EAAI8D,CAE1B,CAEA,MAAO,EACT,CAEA,SAAS6W,EAAmBF,EAAU,CACpC,OAAOrI,EAAW,EAAK7T,GAAKkc,CAAQ,EAAIjd,GAAII,EAAI6c,CAAQ,GAAKnb,EAAQ,YAAc,KAAM6Z,EAAS,IAAW3U,EAAY,OAAO,SAAQ,GAAMlF,EAAQ,eAAiB,EAAE,CAC3K,CAEA,SAAS6a,EAAUna,EAAGgb,EAAY,CAChC,OAAOC,EAAQjb,EAAGgb,CAAU,EAAIC,EAAQC,EAAalb,CAAC,EAAGgb,CAAU,CACrE,CAEA,SAASX,GAASra,EAAG,CACnB,OAAO/C,GAAO+C,CAAC,EAAI/C,GAAOie,EAAalb,CAAC,CAAC,CAC3C,CAEA,SAASkb,EAAalb,EAAG,CACvB,OAAOiZ,IAAcjZ,GAAKkZ,GAAiBD,CAC7C,CAEA,SAASgC,EAAQjb,EAAGgb,EAAY,CAC9B,OAAQnB,EAAa7Z,CAAC,EAAIA,EAAE,eAAe,CAAC,EAAIA,GAAG,OAASuG,EAAQyU,EAAa,IAAM,GAAG,CAAC,CAC7F,CAEA,SAASd,EAAU5M,EAAM,CACvB,OAAOA,GAAQ+L,GAAY7W,EAAQ,GAAG0I,EAAK,EAAItB,GAAW,EAC5D,CAEA,SAASkQ,EAAYqB,EAAS,CAC5B,IAAIC,EAAS9b,EAAQ,OACrB,MAAO,CAAC5E,GAAQygB,EAAS,IAAMzS,GAAwB,MAAQJ,EAAW,IAAM,CAAC8S,GAAU,CAAC1gB,GAAQygB,EAASC,CAAM,EACrH,CAEA,SAASvB,EAAa7Z,EAAG,CACvB,OAAO,OAAO,WAAe,KAAeA,aAAa,UAC3D,CAEA,SAASqb,IAAa,CACpB,OAAOjC,CACT,CAEA,SAASO,GAAQrgB,EAAO,CACtBigB,EAAWjgB,CACb,CAEA,MAAO,CACL,MAAOwR,EACP,QAAS6O,GACT,WAAY0B,EAChB,CACA,CAEA,IAAIC,GAAoB,CACtB,SAAU,IACV,MAAOtV,GACP,KAAMD,GACN,GAAIE,GACJ,KAAMC,EACR,EAEA,SAASqV,GAAargB,EAAK,CACzB,OAAAA,EAAMhC,GAASgC,CAAG,EAAIA,EAAMA,EAAI,IACzBogB,GAAkBpgB,CAAG,GAAKA,CACnC,CAEA,IAAIsgB,GAAiB,UAErB,SAASC,GAASjZ,EAASgC,EAAalF,EAAS,CAC/C,IAAIoc,EAAoBnZ,EAAeC,CAAO,EAC1CG,EAAK+Y,EAAkB,GACvBxc,EAAOwc,EAAkB,KACzB9b,EAAS8b,EAAkB,OAE3BvR,EAAO3H,EAAQ,KACf+D,EAAU/B,EAAY,UAAU,QAChCzN,EACAwiB,EAEJ,SAASzO,GAAQ,CACfD,EAAI,EACJlI,EAAG1B,EAAeZ,CAAO,EACzBsC,EAAG1B,EAAe4J,CAAI,EACtBlI,EAAGlC,GAAY2L,CAAM,CACvB,CAEA,SAASvB,GAAO,CACd,IAAI8Q,EAAWrc,EAAQ,SAEnBqc,IACF5kB,EAAS4kB,IAAa,SAAW,OAASxR,EAC1CjL,EAAKnI,EAAQykB,GAAgBI,CAAS,EAE1C,CAEA,SAASvb,GAAU,CACjBT,EAAO7I,EAAQykB,EAAc,CAC/B,CAEA,SAAS7B,EAAQrgB,EAAO,CACtBigB,EAAWjgB,CACb,CAEA,SAAS8S,GAAS,CAChB,IAAIyP,EAAYtC,EAChBA,EAAW,GACX/gB,GAAS,UAAY,CACnB+gB,EAAWsC,CACb,CAAC,CACH,CAEA,SAASD,EAAU5b,EAAG,CACpB,GAAI,CAACuZ,EAAU,CACb,IAAIre,EAAMqgB,GAAavb,CAAC,EAEpB9E,IAAQqL,EAAQR,EAAU,EAC5BvD,EAAQ,GAAG,GAAG,EACLtH,IAAQqL,EAAQP,EAAW,GACpCxD,EAAQ,GAAG,GAAG,CAElB,CACF,CAEA,MAAO,CACL,MAAOsI,EACP,QAASzK,EACT,QAASsZ,CACb,CACA,CAEA,IAAImC,GAAqB1e,GAAiB,QACtC2e,GAAwBD,GAAqB,UAC7CE,GAAiB,IAAMF,GAAqB,OAASC,GAAwB,IAEjF,SAASE,GAASzZ,EAASgC,EAAalF,EAAS,CAC/C,IAAI4c,EAAoB3Z,EAAeC,CAAO,EAC1CG,EAAKuZ,EAAkB,GACvBC,EAAMD,EAAkB,IACxBhd,EAAOgd,EAAkB,KACzBtZ,EAAOsZ,EAAkB,KAEzBE,EAAe9c,EAAQ,WAAa,aACpCF,EAAS,CAACsB,GAAac,EAAc,EACrC6a,EAAU,CAAA,EAEd,SAASvR,GAAQ,CACXxL,EAAQ,WACVuL,EAAI,EACJlI,EAAG3B,EAAe6J,CAAI,EAE1B,CAEA,SAASA,GAAO,CACd7S,GAAMqkB,CAAO,EACbrX,EAAQ,EAEJoX,EACFE,EAAQ,GAERH,EAAI/c,CAAM,EACVuD,EAAGvD,EAAQmd,CAAK,EAChBA,EAAK,EAET,CAEA,SAASvX,GAAW,CAClBR,EAAY,OAAO,QAAQ,SAAU2L,EAAO,CAC1CpT,GAASoT,EAAM,MAAO6L,EAAc,EAAE,QAAQ,SAAUvN,EAAK,CAC3D,IAAI+N,EAAMngB,GAAaoS,EAAKqN,EAAkB,EAC1CW,EAASpgB,GAAaoS,EAAKsN,EAAqB,EAEpD,GAAIS,IAAQ/N,EAAI,KAAOgO,IAAWhO,EAAI,OAAQ,CAC5C,IAAIlS,EAAY+C,EAAQ,QAAQ,QAC5BlF,EAASqU,EAAI,cACbiO,EAAU7hB,GAAMT,EAAQ,IAAMmC,CAAS,GAAKT,GAAO,OAAQS,EAAWnC,CAAM,EAChFiiB,EAAQ,KAAK,CAAC5N,EAAK0B,EAAOuM,CAAO,CAAC,EAClCjO,EAAI,KAAOvS,GAAQuS,EAAK,MAAM,CAChC,CACF,CAAC,CACH,CAAC,CACH,CAEA,SAAS8N,GAAQ,CACfF,EAAUA,EAAQ,OAAO,SAAU/b,EAAM,CACvC,IAAI+M,EAAW/N,EAAQ,UAAYA,EAAQ,cAAgB,GAAK,GAAK,EACrE,OAAOgB,EAAK,CAAC,EAAE,SAASkC,EAAQ,MAAO6K,CAAQ,EAAIsP,EAAKrc,CAAI,EAAI,EAClE,CAAC,EACD+b,EAAQ,QAAUF,EAAI/c,CAAM,CAC9B,CAEA,SAASud,EAAKrc,EAAM,CAClB,IAAImO,EAAMnO,EAAK,CAAC,EAChBpG,GAASoG,EAAK,CAAC,EAAE,MAAO+I,EAAa,EACrCnK,EAAKuP,EAAK,aAAcnW,EAAMskB,EAAQtc,CAAI,CAAC,EAC3C1E,EAAa6S,EAAK,MAAOpS,GAAaoS,EAAKqN,EAAkB,CAAC,EAC9DlgB,EAAa6S,EAAK,SAAUpS,GAAaoS,EAAKsN,EAAqB,CAAC,EACpEvgB,GAAgBiT,EAAKqN,EAAkB,EACvCtgB,GAAgBiT,EAAKsN,EAAqB,CAC5C,CAEA,SAASa,EAAOtc,EAAMN,EAAG,CACvB,IAAIyO,EAAMnO,EAAK,CAAC,EACZ6P,EAAQ7P,EAAK,CAAC,EAClBtD,GAAYmT,EAAM,MAAO9G,EAAa,EAElCrJ,EAAE,OAAS,UACbvD,GAAO6D,EAAK,CAAC,CAAC,EACdpE,GAAQuS,EAAK,EAAE,EACf7L,EAAKT,GAAuBsM,EAAK0B,CAAK,EACtCvN,EAAK1B,EAAY,GAGnBkb,GAAgBE,EAAQ,CAC1B,CAEA,SAASA,GAAW,CAClBD,EAAQ,QAAUM,EAAKN,EAAQ,MAAK,CAAE,CACxC,CAEA,MAAO,CACL,MAAOvR,EACP,QAASxS,EAAMN,GAAOqkB,CAAO,EAC7B,MAAOE,CACX,CACA,CAEA,SAASM,GAAWra,EAASgC,EAAalF,EAAS,CACjD,IAAIE,EAAQ+C,EAAeC,CAAO,EAC9BG,EAAKnD,EAAM,GACXoD,EAAOpD,EAAM,KACbN,EAAOM,EAAM,KACb+N,EAAS/I,EAAY,OACrByF,EAAWzF,EAAY,SACvB0J,EAAa1J,EAAY,WACzByQ,EAAW/G,EAAW,SACtBoH,EAAWpH,EAAW,SACtBoG,EAAKpG,EAAW,GAChB3H,EAAU/B,EAAY,UAAU,QAChCmR,EAAc1L,EAAS,WACvBrQ,EAAQ,CAAA,EACR8Q,EACAoS,EAEJ,SAAShS,GAAQ,CACfzK,EAAO,EACPsC,EAAG,CAAC1B,EAAeD,EAAesB,EAAuB,EAAGwI,CAAK,EACjE,IAAImL,EAAU3W,EAAQ,WACtBqW,GAAezZ,GAAQyZ,EAAaM,EAAU,GAAK,MAAM,EAErDA,IACFtT,EAAG,CAAClC,GAAYc,GAAcC,EAAc,EAAGgC,CAAM,EACrDuZ,EAAgB,EAChBvZ,EAAM,EACNZ,EAAKf,GAA0B,CAC7B,KAAM6I,EACN,MAAO9Q,CACf,EAASuU,EAAM3L,EAAQ,KAAK,CAAC,EAE3B,CAEA,SAASnC,GAAU,CACbqK,IACFjO,GAAOkZ,EAAczd,GAAMwS,EAAK,QAAQ,EAAIA,CAAI,EAChD1N,GAAY0N,EAAMoS,CAAiB,EACnC9kB,GAAM4B,CAAK,EACX8Q,EAAO,MAGTlL,EAAM,QAAO,CACf,CAEA,SAASud,GAAmB,CAC1B,IAAIvO,EAAShM,EAAQ,OACjBzI,EAAUuF,EAAQ,QAClB8K,EAAO9K,EAAQ,KACf6U,EAAU7U,EAAQ,QAClB7B,EAAMwX,EAAQ,EAAK/G,EAAW,OAAM,EAAK,EAAIvQ,GAAK6Q,EAAS2F,CAAO,EACtEzJ,EAAOiL,GAAe7Z,GAAO,KAAM/B,EAAQ,WAAYkQ,EAAS,MAAM,aAAa,EACnF/P,GAASwQ,EAAMoS,EAAoBrU,GAAmB,KAAOuU,EAAY,CAAE,EAC3EphB,EAAa8O,EAAM7D,GAAM,SAAS,EAClCjL,EAAa8O,EAAMtD,GAAYgD,EAAK,MAAM,EAC1CxO,EAAa8O,EAAMnD,GAAkByV,EAAY,IAAO5W,GAAM,WAAa,EAAE,EAE7E,QAASnP,EAAI,EAAGA,EAAIwG,EAAKxG,IAAK,CAC5B,IAAIgmB,EAAKnhB,GAAO,KAAM,KAAM4O,CAAI,EAC5BwS,EAASphB,GAAO,SAAU,CAC5B,MAAO/B,EAAQ,KACf,KAAM,QACd,EAASkjB,CAAE,EACD5Q,EAAWkB,EAAO,MAAMtW,CAAC,EAAE,IAAI,SAAUkZ,EAAO,CAClD,OAAOA,EAAM,MAAM,EACrB,CAAC,EACGgN,EAAO,CAAClI,KAAcd,EAAU,EAAI/J,EAAK,MAAQA,EAAK,OAC1DlL,EAAKge,EAAQ,QAAS5kB,EAAMmhB,EAASxiB,CAAC,CAAC,EAEnCqI,EAAQ,oBACVJ,EAAKge,EAAQ,UAAW5kB,EAAMsjB,EAAW3kB,CAAC,CAAC,EAG7C2E,EAAaqhB,EAAIpW,GAAM,cAAc,EACrCjL,EAAashB,EAAQrW,GAAM,KAAK,EAChCjL,EAAashB,EAAQjW,GAAeoF,EAAS,KAAK,GAAG,CAAC,EACtDzQ,EAAashB,EAAQ9V,GAAY5I,GAAO2e,EAAMlmB,EAAI,CAAC,CAAC,EACpD2E,EAAashB,EAAQpW,GAAW,EAAE,EAClClN,EAAM,KAAK,CACT,GAAIqjB,EACJ,OAAQC,EACR,KAAMjmB,CACd,CAAO,CACH,CACF,CAEA,SAASwiB,EAAQxL,EAAM,CACrBqG,EAAG,IAAMrG,EAAM,EAAI,CACrB,CAEA,SAAS2N,EAAU3N,EAAMjO,EAAG,CAC1B,IAAIwO,EAAS5U,EAAM,OACfsB,EAAMqgB,GAAavb,CAAC,EACpBod,EAAMJ,EAAY,EAClBK,EAAW,GAEXniB,IAAQqL,EAAQP,GAAa,GAAOoX,CAAG,EACzCC,EAAW,EAAEpP,EAAOO,EACXtT,IAAQqL,EAAQR,GAAY,GAAOqX,CAAG,EAC/CC,GAAY,EAAEpP,EAAOO,GAAUA,EACtBtT,IAAQ,OACjBmiB,EAAW,EACFniB,IAAQ,QACjBmiB,EAAW7O,EAAS,GAGtB,IAAI8O,EAAO1jB,EAAMyjB,CAAQ,EAErBC,IACFlhB,GAAMkhB,EAAK,MAAM,EACjBhJ,EAAG,IAAM+I,CAAQ,EACjBzgB,GAAQoD,EAAG,EAAI,EAEnB,CAEA,SAASgd,GAAe,CACtB,OAAO1d,EAAQ,qBAAuBA,EAAQ,SAChD,CAEA,SAAS6O,EAAM1H,EAAO,CACpB,OAAO7M,EAAMsU,EAAW,OAAOzH,CAAK,CAAC,CACvC,CAEA,SAASjD,GAAS,CAChB,IAAIwO,EAAO7D,EAAMmH,EAAS,EAAI,CAAC,EAC3B/I,EAAO4B,EAAMmH,GAAU,EAE3B,GAAItD,EAAM,CACR,IAAIkL,EAASlL,EAAK,OAClBhV,GAAYkgB,EAAQjU,EAAY,EAChCzN,GAAgB0hB,EAAQ/V,EAAa,EACrCvL,EAAashB,EAAQpW,GAAW,EAAE,CACpC,CAEA,GAAIyF,EAAM,CACR,IAAIgR,EAAUhR,EAAK,OACnBrS,GAASqjB,EAAStU,EAAY,EAC9BrN,EAAa2hB,EAASpW,GAAe,EAAI,EACzCvL,EAAa2hB,EAASzW,GAAW,EAAE,CACrC,CAEAlE,EAAKd,GAA0B,CAC7B,KAAM4I,EACN,MAAO9Q,CACb,EAAOoY,EAAMzF,CAAI,CACf,CAEA,MAAO,CACL,MAAO3S,EACP,MAAOkR,EACP,QAASzK,EACT,MAAO8N,EACP,OAAQ3K,CACZ,CACA,CAEA,IAAIga,GAAe,CAAC,IAAK,OAAO,EAEhC,SAASC,GAAKjb,EAASgC,EAAalF,EAAS,CAC3C,IAAImM,EAAenM,EAAQ,aACvBsM,EAAatM,EAAQ,WACrBF,EAAS,CAAA,EAEb,SAAS0L,GAAQ,CACftI,EAAQ,QAAQ,QAAQ,SAAUzL,EAAQ,CACnCA,EAAO,WACV2mB,EAAKlb,EAASzL,EAAO,MAAM,EAC3B2mB,EAAK3mB,EAAO,OAAQyL,CAAO,EAE/B,CAAC,EAEGiJ,GACFkS,EAAQ,CAEZ,CAEA,SAAStd,GAAU,CACjBjB,EAAO,QAAQ,SAAUI,EAAO,CAC9BA,EAAM,QAAO,CACf,CAAC,EACDxH,GAAMoH,CAAM,CACd,CAEA,SAASwR,GAAU,CACjBvQ,EAAO,EACPyK,EAAK,CACP,CAEA,SAAS4S,EAAKE,EAAQ7mB,EAAQ,CAC5B,IAAIyI,EAAQ+C,EAAeqb,CAAM,EACjCpe,EAAM,GAAGiB,GAAY,SAAUgG,EAAOuL,EAAMD,EAAM,CAChDhb,EAAO,GAAGA,EAAO,GAAGoU,EAAI,EAAI4G,EAAOtL,CAAK,CAC1C,CAAC,EACDrH,EAAO,KAAKI,CAAK,CACnB,CAEA,SAASme,GAAW,CAClB,IAAIne,EAAQ+C,EAAeC,CAAO,EAC9BG,EAAKnD,EAAM,GACfmD,EAAGhC,GAAa8Y,CAAO,EACvB9W,EAAGP,GAAqBwZ,CAAS,EACjCjZ,EAAG,CAACpC,GAAeU,CAAa,EAAGuC,CAAM,EACzCpE,EAAO,KAAKI,CAAK,EACjBA,EAAM,KAAKuC,GAA0BS,EAAQ,OAAO,CACtD,CAEA,SAASgB,GAAS,CAChB5H,EAAa4I,EAAY,SAAS,KAAM+C,GAAkBjI,EAAQ,YAAc8G,GAAM,WAAa,EAAE,CACvG,CAEA,SAASqT,EAAQtJ,EAAO,CACtB3N,EAAQ,GAAG2N,EAAM,KAAK,CACxB,CAEA,SAASyL,EAAUzL,EAAOnQ,EAAG,CACvBtG,GAAS8jB,GAAcjC,GAAavb,CAAC,CAAC,IACxCyZ,EAAQtJ,CAAK,EACbvT,GAAQoD,CAAC,EAEb,CAEA,MAAO,CACL,MAAO1H,EAAMkM,EAAY,MAAM,IAAK,CAClC,WAAYrL,GAAYyS,CAAU,EAAIH,EAAeG,CAC3D,EAAO,EAAI,EACP,MAAOd,EACP,QAASzK,EACT,QAASuQ,CACb,CACA,CAEA,SAASiN,GAAMrb,EAASgC,EAAalF,EAAS,CAC5C,IAAIwe,EAAoBvb,EAAeC,CAAO,EAC1CtD,EAAO4e,EAAkB,KAEzBC,EAAW,EAEf,SAASjT,GAAQ,CACXxL,EAAQ,OACVJ,EAAKsF,EAAY,SAAS,MAAO,QAASwZ,EAASpF,EAAuB,CAE9E,CAEA,SAASoF,EAAQhe,EAAG,CAClB,GAAIA,EAAE,WAAY,CAChB,IAAIie,EAASje,EAAE,OACX6S,EAAYoL,EAAS,EACrBC,EAAYjhB,GAAO+C,CAAC,EAEpBme,EAAO7e,EAAQ,mBAAqB,EAEpC8e,EAAQ9e,EAAQ,YAAc,EAE9B1B,EAAIqgB,CAAM,EAAIE,GAAQD,EAAYH,EAAWK,IAC/C5b,EAAQ,GAAGqQ,EAAY,IAAM,GAAG,EAChCkL,EAAWG,GAGbG,EAAcxL,CAAS,GAAKjW,GAAQoD,CAAC,CACvC,CACF,CAEA,SAASqe,EAAcxL,EAAW,CAChC,MAAO,CAACvT,EAAQ,cAAgBkD,EAAQ,MAAM,GAAG7K,EAAM,GAAK6M,EAAY,WAAW,YAAYqO,CAAS,IAAM,EAChH,CAEA,MAAO,CACL,MAAO/H,CACX,CACA,CAEA,IAAIwT,GAAmB,GAEvB,SAASC,GAAK/b,EAASgC,EAAalF,EAAS,CAC3C,IAAIkf,EAAoBjc,EAAeC,CAAO,EAC1CG,EAAK6b,EAAkB,GAEvB/T,EAAQjG,EAAY,SAAS,MAC7ByR,EAAU3W,EAAQ,MAAQ,CAACA,EAAQ,aACnCmf,EAAK3iB,GAAO,OAAQiN,EAAQ,EAC5BjG,EAAWD,GAAgByb,GAAkBhmB,EAAMwe,EAAQ,EAAK,CAAC,EAErE,SAAShM,GAAQ,CACXmL,IACF0D,EAAQ,CAACnV,EAAY,SAAS,SAAQ,CAAE,EACxC5I,EAAa6O,EAAO9C,GAAa,EAAI,EACrC8W,EAAG,YAAc,IACjB9b,EAAGX,GAAqB1J,EAAMqhB,EAAS,EAAI,CAAC,EAC5ChX,EAAGT,GAAsB5J,EAAMqhB,EAAS,EAAK,CAAC,EAC9ChX,EAAG,CAACjC,GAAac,EAAc,EAAGlJ,EAAMwe,EAAQ,EAAI,CAAC,EAEzD,CAEA,SAASA,EAAOpK,EAAQ,CACtB9Q,EAAa6O,EAAO/C,GAAWgF,CAAM,EAEjCA,GACFvS,GAAOsQ,EAAOgU,CAAE,EAChB3b,EAAS,MAAK,IAEdrG,GAAOgiB,CAAE,EACT3b,EAAS,OAAM,EAEnB,CAEA,SAASzC,GAAU,CACjB7E,GAAgBiP,EAAO,CAAChD,GAAWE,GAAaD,EAAS,CAAC,EAC1DjL,GAAOgiB,CAAE,CACX,CAEA,SAAS9E,EAAQJ,EAAU,CACrBtD,GACFra,EAAa6O,EAAOhD,GAAW8R,EAAW,MAAQ,QAAQ,CAE9D,CAEA,MAAO,CACL,MAAOzO,EACP,QAAS6O,EACT,QAAStZ,CACb,CACA,CAEA,IAAIqe,GAAqC,OAAO,OAAO,CACrD,UAAW,KACX,MAAOna,GACP,UAAW+B,GACX,SAAU2D,GACV,OAAQsD,GACR,OAAQqB,GACR,OAAQ6B,GACR,KAAMa,GACN,WAAYpD,GACZ,OAAQwH,GACR,SAAUgB,GACV,MAAOa,GACP,OAAQS,GACR,KAAMa,GACN,SAAU4C,GACV,SAAUQ,GACV,WAAYY,GACZ,KAAMY,GACN,MAAOI,GACP,KAAMU,EACR,CAAC,EACGI,GAAO,CACT,KAAM,iBACN,KAAM,aACN,MAAO,oBACP,KAAM,mBACN,OAAQ,iBACR,MAAO,gBACP,KAAM,iBACN,MAAO,iBACP,SAAU,WACV,MAAO,QACP,OAAQ,yBACR,WAAY,UACd,EACIC,GAAW,CACb,KAAM,QACN,KAAM,SACN,MAAO,IACP,QAAS,EACT,YAAa,GACb,OAAQ,GACR,WAAY,GACZ,mBAAoB,GACpB,SAAU,IACV,aAAc,GACd,aAAc,GACd,cAAe,GACf,OAAQ,gCACR,KAAM,GACN,UAAW,MACX,UAAW,GACX,eAAgB,6CAChB,KAAM,GACN,QAASnV,GACT,KAAMkV,GACN,cAAe,CACb,MAAO,EACP,YAAa,EACb,SAAU,OACd,CACA,EAEA,SAASE,GAAKrc,EAASgC,EAAalF,EAAS,CAC3C,IAAIiO,EAAS/I,EAAY,OAEzB,SAASsG,GAAQ,CACfvI,EAAeC,CAAO,EAAE,GAAG,CAACjC,GAAeS,CAAa,EAAG6J,CAAI,CACjE,CAEA,SAASA,GAAO,CACd0C,EAAO,QAAQ,SAAU4C,EAAO,CAC9BA,EAAM,MAAM,YAAa,eAAiB,IAAMA,EAAM,MAAQ,IAAI,CACpE,CAAC,CACH,CAEA,SAAS/X,EAAMqO,EAAOqY,EAAM,CAC1BvR,EAAO,MAAM,aAAc,WAAajO,EAAQ,MAAQ,MAAQA,EAAQ,MAAM,EAC9E9G,GAASsmB,CAAI,CACf,CAEA,MAAO,CACL,MAAOhU,EACP,MAAO1S,EACP,OAAQK,EACZ,CACA,CAEA,SAAS0X,GAAM3N,EAASgC,EAAalF,EAAS,CAC5C,IAAIgS,EAAO9M,EAAY,KACnB0J,EAAa1J,EAAY,WACzBwT,EAASxT,EAAY,OACrBkG,EAAOlG,EAAY,SAAS,KAC5Bua,EAAazmB,EAAM0D,GAAO0O,EAAM,YAAY,EAC5CsU,EAEJ,SAASlU,GAAQ,CACfvI,EAAeC,CAAO,EAAE,KAAKkI,EAAM,gBAAiB,SAAU1K,EAAG,CAC3DA,EAAE,SAAW0K,GAAQsU,IACvBrb,EAAM,EACNqb,EAAW,EAEf,CAAC,CACH,CAEA,SAAS5mB,EAAMqO,EAAOqY,EAAM,CAC1B,IAAItM,EAAclB,EAAK,WAAW7K,EAAO,EAAI,EACzC6L,EAAWhB,EAAK,YAAW,EAC3B2N,EAAQC,EAASzY,CAAK,EAEtB7I,EAAI4U,EAAcF,CAAQ,GAAK,GAAK2M,GAAS,EAC3C3f,EAAQ,UACV0Y,EAAO,OAAOxF,EAAayM,EAAO,GAAOH,CAAI,GAE7CC,EAAW,aAAeE,EAAQ,MAAQ3f,EAAQ,MAAM,EACxDgS,EAAK,UAAUkB,EAAa,EAAI,EAChCwM,EAAcF,IAGhBxN,EAAK,KAAK7K,CAAK,EACfqY,EAAI,EAER,CAEA,SAASnb,GAAS,CAChBob,EAAW,EAAE,EACb/G,EAAO,OAAM,CACf,CAEA,SAASkH,EAASzY,EAAO,CACvB,IAAI0Y,EAAc7f,EAAQ,YAE1B,GAAIkD,EAAQ,GAAG0I,EAAK,GAAKiU,EAAa,CACpC,IAAInN,EAAO9D,EAAW,SAAS,EAAI,EAC/B7V,EAAM6V,EAAW,OAAM,EAE3B,GAAI8D,IAAS,GAAKvL,GAASpO,GAAO2Z,GAAQ3Z,GAAOoO,IAAU,EACzD,OAAO0Y,CAEX,CAEA,OAAO7f,EAAQ,KACjB,CAEA,MAAO,CACL,MAAOwL,EACP,MAAO1S,EACP,OAAQuL,CACZ,CACA,CAEA,IAAIyb,IAAuB,UAAY,CACrC,SAASA,EAAQroB,EAAQuI,EAAS,CAChC,KAAK,MAAQiD,EAAc,EAC3B,KAAK,WAAa,CAAA,EAClB,KAAK,MAAQyB,GAAMxM,EAAO,EAC1B,KAAK,QAAU,CAAA,EACf,KAAK,GAAK,CAAA,EACV,KAAK,GAAK,CAAA,EACV,IAAI2S,EAAOjR,GAASnC,CAAM,EAAI+F,GAAM,SAAU/F,CAAM,EAAIA,EACxDsG,GAAO8M,EAAMA,EAAO,cAAc,EAClC,KAAK,KAAOA,EACZ7K,EAAUjE,GAAM,CACd,MAAOgB,GAAa8N,EAAM/C,EAAU,GAAK,GACzC,WAAY/K,GAAa8N,EAAM9C,EAAe,GAAK,EACzD,EAAOuX,GAAUQ,EAAQ,SAAU9f,GAAW,CAAA,CAAE,EAE5C,GAAI,CACFjE,GAAMiE,EAAS,KAAK,MAAMjD,GAAa8N,EAAM/M,EAAc,CAAC,CAAC,CAC/D,MAAY,CACVC,GAAO,GAAO,cAAc,CAC9B,CAEA,KAAK,GAAK,OAAO,OAAOhC,GAAM,CAAA,EAAIiE,CAAO,CAAC,CAC5C,CAEA,IAAI+f,EAASD,EAAQ,UAErB,OAAAC,EAAO,MAAQ,SAAeC,EAAY3N,EAAY,CACpD,IAAI4N,EAAQ,KAERrb,EAAQ,KAAK,MACbM,EAAc,KAAK,WACvBnH,GAAO6G,EAAM,GAAG,CAAC1M,GAASM,EAAS,CAAC,EAAG,kBAAkB,EACzDoM,EAAM,IAAI1M,EAAO,EACjB,KAAK,GAAKgN,EACV,KAAK,GAAKmN,GAAc,KAAK,KAAO,KAAK,GAAGvG,EAAI,EAAIyT,GAAO1O,IAC3D,KAAK,GAAKmP,GAAc,KAAK,GAC7B,IAAIE,EAAerkB,GAAO,CAAA,EAAIujB,GAAuB,KAAK,GAAI,CAC5D,WAAY,KAAK,EACvB,CAAK,EACD,OAAA3jB,GAAOykB,EAAc,SAAUC,EAAWvkB,EAAK,CAC7C,IAAIwkB,EAAYD,EAAUF,EAAO/a,EAAa+a,EAAM,EAAE,EACtD/a,EAAYtJ,CAAG,EAAIwkB,EACnBA,EAAU,OAASA,EAAU,MAAK,CACpC,CAAC,EACD3kB,GAAOyJ,EAAa,SAAUkb,EAAW,CACvCA,EAAU,OAASA,EAAU,MAAK,CACpC,CAAC,EACD,KAAK,KAAKnf,EAAa,EACvBrG,GAAS,KAAK,KAAM8O,EAAiB,EACrC9E,EAAM,IAAIxM,EAAI,EACd,KAAK,KAAK8I,EAAW,EACd,IACT,EAEA6e,EAAO,KAAO,SAAczB,EAAQ,CAClC,YAAK,QAAQ,KAAK,CAChB,OAAQA,CACd,CAAK,EACDA,EAAO,QAAQ,KAAK,CAClB,OAAQ,KACR,SAAU,EAChB,CAAK,EAEG,KAAK,MAAM,GAAGlmB,EAAI,IACpB,KAAK,GAAG,KAAK,QAAO,EAEpBkmB,EAAO,WAAW,KAAK,QAAO,GAGzB,IACT,EAEAyB,EAAO,GAAK,SAAY9K,EAAS,CAC/B,YAAK,GAAG,WAAW,GAAGA,CAAO,EAEtB,IACT,EAEA8K,EAAO,GAAK,SAAYjgB,EAAQC,EAAU,CACxC,YAAK,MAAM,GAAGD,EAAQC,CAAQ,EACvB,IACT,EAEAggB,EAAO,IAAM,SAAajgB,EAAQ,CAChC,YAAK,MAAM,IAAIA,CAAM,EACd,IACT,EAEAigB,EAAO,KAAO,SAAc7f,EAAO,CACjC,IAAImgB,EAEJ,OAACA,EAAc,KAAK,OAAO,KAAK,MAAMA,EAAa,CAACngB,CAAK,EAAE,OAAOtH,GAAM,UAAW,CAAC,CAAC,CAAC,EAE/E,IACT,EAEAmnB,EAAO,IAAM,SAAa/U,EAAQ7D,EAAO,CACvC,YAAK,GAAG,OAAO,IAAI6D,EAAQ7D,CAAK,EAEzB,IACT,EAEA4Y,EAAO,OAAS,SAAgB/Q,EAAS,CACvC,YAAK,GAAG,OAAO,OAAOA,CAAO,EAEtB,IACT,EAEA+Q,EAAO,GAAK,SAAYzmB,EAAM,CAC5B,OAAO,KAAK,GAAG,OAASA,CAC1B,EAEAymB,EAAO,QAAU,UAAmB,CAClC,YAAK,KAAKre,CAAa,EAChB,IACT,EAEAqe,EAAO,QAAU,SAAiBpa,EAAY,CACxCA,IAAe,SACjBA,EAAa,IAGf,IAAIzF,EAAQ,KAAK,MACb0E,EAAQ,KAAK,MAEjB,OAAIA,EAAM,GAAG1M,EAAO,EAClB+K,EAAe,IAAI,EAAE,GAAG/B,GAAa,KAAK,QAAQ,KAAK,KAAMyE,CAAU,CAAC,GAExElK,GAAO,KAAK,GAAI,SAAU2kB,EAAW,CACnCA,EAAU,SAAWA,EAAU,QAAQza,CAAU,CACnD,EAAG,EAAI,EACPzF,EAAM,KAAKkC,EAAa,EACxBlC,EAAM,QAAO,EACbyF,GAAcjN,GAAM,KAAK,OAAO,EAChCkM,EAAM,IAAIpM,EAAS,GAGd,IACT,EAEAX,GAAaioB,EAAS,CAAC,CACrB,IAAK,UACL,IAAK,UAAe,CAClB,OAAO,KAAK,EACd,EACA,IAAK,SAAa9f,EAAS,CACzB,KAAK,GAAG,MAAM,IAAIA,EAAS,GAAM,EAAI,CACvC,CACJ,EAAK,CACD,IAAK,SACL,IAAK,UAAe,CAClB,OAAO,KAAK,GAAG,OAAO,UAAU,EAAI,CACtC,CACJ,EAAK,CACD,IAAK,QACL,IAAK,UAAe,CAClB,OAAO,KAAK,GAAG,WAAW,SAAQ,CACpC,CACJ,CAAG,CAAC,EAEK8f,CACT,GAAC,EAEGQ,GAASR,GACbQ,GAAO,SAAW,CAAA,EAClBA,GAAO,OAAS7nB,GCxkGT,MAAM8nB,GAAiBllB,GAAqC,CAC/D,GAAI,CACA,OAAO,SAAS,cAAcA,CAAQ,CAC1C,MAAQ,CACJ,MACJ,CACJ,EAKamlB,GAAoBnlB,GAA0C,CACvE,GAAI,CACA,OAAO,SAAS,iBAAiBA,CAAQ,CAC7C,MAAQ,CACJ,OAAO,SAAS,iBAAiB,EAAE,CACvC,CACJ,EAKaolB,GAAkB1c,GAAmC,CAC9D,GAAI,CACA,OAAO,SAAS,eAAeA,CAAE,CACrC,MAAQ,CACJ,MACJ,CACJ,EAKa2c,GAAgB,CACzBC,EACA3gB,EAA6B,CAAA,EAC7B4gB,EAAY,KACE,CACd,GAAI,CACA,MAAMC,EAAU,SAAS,cAAcF,CAAO,EAG9C,SAAW,CAAC/kB,EAAK5B,CAAK,IAAK,OAAO,QAAQgG,CAAO,EACzCpE,IAAQ,YACRilB,EAAQ,UAAY,OAAO7mB,CAAK,EACzB4B,IAAQ,KACfilB,EAAQ,GAAK,OAAO7mB,CAAK,EAEzB6mB,EAAQ,aAAajlB,EAAK,OAAO5B,CAAK,CAAC,EAI/C,OAAI4mB,IACAC,EAAQ,UAAYD,GAGjBC,CACX,MAAQ,CACJ,OAAO,SAAS,cAAc,KAAK,CACvC,CACJ,EAKaC,GAAc,CAAChmB,EAAiBS,IAAyB,CAClE,GAAI,CACAT,EAAO,YAAYS,CAAK,CAC5B,MAAQ,CAER,CACJ,EAKawlB,GAAe,CAACjmB,EAAiBS,IAAyB,CACnE,GAAI,CACAT,EAAO,QAAQS,CAAK,CACxB,MAAQ,CAER,CACJ,EAKaylB,GAAiBH,GAA2B,CACrD,GAAI,CACAA,EAAQ,OAAA,CACZ,MAAQ,CAER,CACJ,EAKaI,GAAY,CAACJ,EAAsBtU,IAA+B,CAC3E,GAAI,CACA,SAAW,CAAC2U,EAAUlnB,CAAK,IAAK,OAAO,QAAQuS,CAAM,EACjDsU,EAAQ,MAAM,YAAYK,EAAU,OAAOlnB,CAAK,CAAC,CAEzD,MAAQ,CAER,CACJ,EC9GamnB,GAAmB,CAC9B,wBAAyB,EACzB,yBAA0B,CAC5B,EAGaC,GAAgB,CAC3B,OAAQ,CACN,IAAK,OACL,SAAU,CAAA,EAEZ,QAAS,CACP,IAAK,OACL,SAAU,CAAA,EAEZ,kBAAmB,GACrB,EAGaC,GAAyB,CACpC,oBAAqB,EACrB,wBAAyB,EACzB,kBAAmB,IACnB,iBAAkB,IAClB,IAAK,OACL,kBAAmB,GACrB,EAGaC,GAAiB,CAC5B,sBAAuB,GACvB,kBAAmB,GACrB,EAYaC,GAAkB,CAC7B,aAAc,EACd,YAAa,EACb,gBAAiB,GACjB,iBAAkB,EAClB,iBAAkB,EACpB,EAGaC,GAAS,CACpB,eAAgB,GAChB,oBAAqB,GACvB,EAGaC,GAAe,CAC1B,wBAAyB,qBACzB,sBAAuB,kBACzB,EA4BaC,GAAmB,CAC9B,GAAI,uBACJ,mBAAoB,sBACtB,ECxFaC,GAAiB,IAAe,CACzC,GAAI,CACA,KAAM,CAAE,UAAAC,GAAc,UAKtB,OAJwBA,EAAU,UAC9BT,GAAiB,wBACjBA,GAAiB,wBAAA,IAEM,MAC/B,MAAQ,CACJ,MAAO,EACX,CACJ,ECZaU,GAA4B,CACvC,IAAM,aACN,IAAK,CACH,YAAaH,GAAiB,GAC9B,kBAAmBA,GAAiB,kBAAA,EAEtC,SAAU,CACR,iBAAkBN,GAAc,kBAChC,cAAeI,GAAO,eACtB,kBAAmBA,GAAO,oBAC1B,OAAQ,CACN,IAAKJ,GAAc,OAAO,IAC1B,QAASA,GAAc,OAAO,QAAA,EAEhC,QAAS,CACP,IAAKA,GAAc,QAAQ,IAC3B,QAASA,GAAc,QAAQ,QAAA,EAEjC,SAAU,CACR,iBAAkBC,GAAuB,oBACzC,eAAgB,GAChB,iBAAkBA,GAAuB,kBACzC,eAAgB,GAChB,gBAAiBA,GAAuB,iBACxC,IAAKA,GAAuB,IAC5B,kBAAmB,GACnB,iBAAkB,GAClB,eAAgB,EAAA,CAClB,EAEF,GAAI,CACF,eAAgBI,GAAa,wBAC7B,aAAcA,GAAa,qBAAA,CAE/B,ECvCMK,GAAe,uBAKfC,GAAqBnmB,GAAyB,CAChD,GAAI,CACA,MAAMomB,EAAQC,IAAOA,GAAI,MACnBC,EAASF,GAASA,EAAM,UAC9B,OAAI,OAAOE,GAAW,WACXA,EAAO,KAAKF,EAAOpmB,CAAG,EAEjC,MACJ,MAAQ,CACJ,MACJ,CACJ,EAKMumB,GAAa,CAASC,EAAoBC,IAAiC,CAC7E,MAAMroB,EAAQ+nB,GAAkB,GAAGD,EAAY,IAAIM,CAAU,EAAE,EACzDE,EAAqB,EAE3B,GAAI,OAAOtoB,EAAU,KAAeA,IAAU,KAAM,CAEhD,GAAI,OAAOqoB,GAAiB,UACxB,OAAQroB,IAAU,IAAQA,IAAU,KAAOA,IAAUsoB,EAGzD,GAAI,OAAOD,GAAiB,SAAU,CAClC,MAAME,EAAW,OAAOvoB,CAAK,EAC7B,OAAI,OAAO,MAAMuoB,CAAQ,EACdF,EAEJE,CACX,CAEA,OAAOvoB,CACX,CACA,OAAOqoB,CACX,EAKaG,GAA0B,IAA4B,CAC/D,MAAMC,EAAWZ,GAAc,SAAS,SAExC,MAAO,CACH,iBAAkBM,GAAW,iCAAkCM,EAAS,gBAAgB,EACxF,eAAgBN,GAAW,+BAAgCM,EAAS,cAAc,EAClF,iBAAkBN,GAAW,iCAAkCM,EAAS,gBAAgB,EACxF,eAAgBN,GAAW,+BAAgCM,EAAS,cAAc,EAClF,gBAAiBN,GAAW,gCAAiCM,EAAS,eAAe,EACrF,IAAKN,GAAW,oBAAqBM,EAAS,GAAG,EACjD,kBAAmBN,GAAW,kCAAmCM,EAAS,iBAAiB,EAC3F,iBAAkBN,GAAW,iCAAkCM,EAAS,gBAAgB,EACxF,eAAgBN,GAAW,+BAAgCM,EAAS,cAAc,CAAA,CAE1F,ECrDO,MAAMC,EAAgB,CAKzB,sBAA6B,CACzB,GAAI,CACA,GAAIC,GAAwBd,GAAc,GAAG,cAAc,EACvD,OAIJ,MAAMe,EAAWC,GAA0B,UAAU,EACjDD,EAAS,OAASrB,GAAgB,aAClC,KAAK,gBAAgBqB,CAAQ,EAG7B,KAAK,0BAAA,CAEb,MAAQ,CAER,CACJ,CAKQ,2BAAkC,CAGtC,IAAIE,EAAW,EAEf,MAAMC,EAAkB,IAAY,CAChCD,GAAYvB,GAAgB,iBAC5B,MAAMqB,EAAWC,GAA0B,UAAU,EAEjDD,EAAS,OAASrB,GAAgB,aAElC,KAAK,gBAAgBqB,CAAQ,EACtBE,EAAW,IAElB,WAAWC,EAAiB,GAAe,CAGnD,EAEAA,EAAA,CACJ,CAKQ,gBAAgBH,EAAqC,CACzD,GAAI,CACA,MAAMlW,EAAY,KAAK,yBAAA,EACvB,GAAI,CAACA,EACD,OAGJ,MAAM4R,EAAS,KAAK,gBAAgB5R,CAAS,EAC7C,GAAI,CAAC4R,EACD,OAGJ,MAAMhI,EAAU,KAAK,uBAAuBgI,CAAM,EAClD,GAAI,CAAChI,EACD,OAGJ,KAAK,kBAAkBA,EAASsM,CAAQ,EACxC,KAAK,mBAAmBlW,CAAS,EACjC,KAAK,oBAAoBA,CAAS,EAClC,KAAK,uBAAA,EACL,KAAK,kBAAA,EACL,KAAK,oBAAA,EAGL,KAAK,wBAAA,CACT,MAAQ,CAER,CACJ,CAKQ,0BAAwC,CAC5C,MAAMA,EAAYsW,GAAuB,MAAO,CAC5C,UAAW,qBACX,GAAInB,GAAc,GAAG,cAAA,CACxB,EAEKoB,EAAgBD,GAAuB,MAAO,CAChD,UAAW,uBAAA,CACd,EAEDE,OAAAA,GAAqBxW,EAAWuW,CAAa,EACtCvW,CACX,CAKQ,gBAAgBA,EAAqC,CACzD,MAAM4R,EAAS0E,GAAuB,MAAO,CACzC,UAAW,kBAAA,CACd,EAGDE,OAAAA,GAAqBxW,EAAW4R,CAAM,EAE/BA,CACX,CAKQ,uBAAuBA,EAAkC,CAC7D,MAAMnT,EAAQ6X,GAAuB,MAAO,CACxC,UAAW,eAAA,CACd,EACDE,GAAqB5E,EAAQnT,CAAK,EAElC,MAAMmL,EAAU0M,GAAuB,KAAM,CACzC,UAAW,eACX,GAAInB,GAAc,GAAG,YAAA,CACxB,EACDqB,OAAAA,GAAqB/X,EAAOmL,CAAO,EAC5BA,CACX,CAKQ,kBAAkBA,EAAsBsM,EAAqC,CACjF,MAAMO,EAAWxB,GAAA,EAEjB,UAAWllB,KAAOmmB,EAAU,CACxB,MAAMQ,EAAa3mB,EACb4mB,EAAU,KAAK,eAAeD,CAAU,EAE9C,GAAIC,EAAS,CACT,MAAMpX,EAAQ,KAAK,eAAeoX,EAASF,CAAQ,EACnDD,GAAqB5M,EAASrK,CAAK,CACvC,CACJ,CACJ,CAKQ,eAAexP,EAAkC,CACrD,MAAM6mB,EAAc7mB,EAAI,cAAc,GAAG,EACnC8mB,EAAc9mB,EAAI,cAAc,eAAe,EAC/C+mB,EAAc/mB,EAAI,cAAc,sBAAsB,EAE5D,GAAI,CAAC6mB,GAAe,CAACC,EACjB,OAIJ,MAAME,EAAkB,KAAK,sBAAsBH,EAAY,KAAM7mB,CAAG,EAClEinB,EAAgB,WAAW,iBAAiBjnB,CAAG,EAC/CknB,EAAaF,GAAmBC,EAAc,WAEpD,IAAIE,EAAc,GACdC,EAAY,GAChB,OAAIL,IACAI,EAAcJ,EAAY,aAAe,GACzCK,EAAY,WAAW,iBAAiBL,CAAW,EAAE,OAGlD,CACH,IAAKF,EAAY,KACjB,WAAAK,EACA,KAAMJ,EAAY,aAAe,GACjC,UAAW,WAAW,iBAAiBA,CAAW,EAAE,MACpD,YAAAK,EACA,UAAAC,CAAA,CAER,CAKQ,sBAAsBC,EAAgBV,EAAwC,CAClF,GAAI,CAGA,MAAMW,EADM,IAAI,IAAID,EAAQ,WAAW,SAAS,MAAM,EACpC,SAAS,MAAM,GAAG,EAAE,OAAO,OAAO,EAC9CE,EAASD,EAAM,QAAQ,GAAG,EAC1BE,EAAYF,EAAM,QAAQ,MAAM,EAEtC,IAAIG,EAAO,GAUX,GARIF,IAAWzC,GAAgB,iBAAmBwC,EAAMC,EAASzC,GAAgB,gBAAgB,EAC7F2C,EAAOH,EAAMC,EAASzC,GAAgB,gBAAgB,EAC/C0C,IAAc1C,GAAgB,iBAAmBwC,EAAME,EAAY1C,GAAgB,gBAAgB,EAC1G2C,EAAOH,EAAME,EAAY1C,GAAgB,gBAAgB,EAClDwC,EAAM,OAASxC,GAAgB,eACtC2C,EAAOH,EAAMA,EAAM,OAASxC,GAAgB,gBAAgB,GAG5D,CAAC2C,EACD,OAIJ,MAAMC,EAAQ,KAAK,0BAA0BD,CAAI,EAEjD,OAAIC,EACO,OAAOA,CAAK,IAGvB,MACJ,MAAQ,CAEJ,MAAMC,EAAmBhB,EAAW,MAAM,WAC1C,OAAIgB,GAAoBA,EAAiB,SAAS,MAAM,EAC7CA,EAEX,MACJ,CACJ,CAKQ,0BAA0BF,EAA6B,CAC3D,GAAI,CAGA,MAAMG,EADOpC,GAAI,MAAM,IAAI,MAAM,EACX,KAAMqC,GAAqB,CAC7C,MAAMC,EAAYD,EAClB,IAAIE,EAAU,GAEd,OAAI,OAAOD,EAAU,MAAS,WAC1BC,EAAUD,EAAU,KAAA,EACbA,EAAU,WAAa,OAAOA,EAAU,WAAc,aAC7DC,EAAUD,EAAU,UAAU,MAAM,GAGjCC,IAAYN,CACvB,CAAC,EAED,GAAI,CAACG,EACD,OAIJ,MAAME,EAAYF,EAElB,GAAIE,EAAU,WAAa,OAAOA,EAAU,WAAc,WAAY,CAClE,MAAMJ,EAAQI,EAAU,UAAU,yBAAyB,EAC3D,GAAIJ,EACA,OAAOA,CAEf,CAEA,MACJ,MAAQ,CACJ,MACJ,CACJ,CAKQ,eAAed,EAAkBF,EAAgC,CACrE,MAAMlX,EAAQ+W,GAAuB,KAAM,CACvC,UAAW,iCAAA,CACd,EAED,IAAIyB,EAAa,0BACbtB,IACAsB,EAAa,kCAGjB,MAAMC,EAAkB,cAAcrB,EAAQ,UAAU,oFAGlDsB,EAAqB,KAAK,mBAAmBtB,EAAQ,UAAU,EAGrE,IAAIuB,EAAc,GAClB,OAAKD,IACDC,EAAc;AAAA,gEACsCvB,EAAQ,SAAS;AAAA,kBAC/DA,EAAQ,IAAI;AAAA;AAAA,WAKtBpX,EAAM,UAAY;AAAA,uBACHoX,EAAQ,GAAG;AAAA,8BACJoB,CAAU,YAAYC,CAAe;AAAA,sBAC7CE,CAAW;AAAA;AAAA;AAAA,UAKlB3Y,CACX,CAKQ,mBAAmB0X,EAA6B,CACpD,OAAKA,EAKEA,EAAW,SAAS,MAAM,GAAK,CAACA,EAAW,SAAS,OAAO,EAJvD,EAKf,CAKQ,mBAAmBjX,EAA8B,CACrD,MAAMmY,EAAiBC,GAAuB,uCAAuC,EACjFD,GACAE,GAAsBF,EAAgBnY,CAAS,CAEvD,CAKQ,oBAAoBA,EAA8B,CACtD,MAAMuW,EAAgBvW,EAAU,cAAc,wBAAwB,EACtE,GAAIuW,EAAe,CACf,MAAM+B,EAAehC,GAAuB,MAAO,CAC/C,UAAW,kBAAA,EACZ,yCAAyC,EAE5C+B,GAAsB9B,EAAe+B,CAAY,EAEjD,MAAMC,EAAgB,KAAK,wBAAA,EAC3BhC,EAAc,mBAAmB,YAAagC,CAAa,CAC/D,CACJ,CAKQ,yBAAkC,CACtC,KAAM,CAAE,YAAAC,GAAgBrD,GAAc,IAgChCoD,EA7BkB,CACpB,CACI,OAAQ,GAAGC,CAAW,iBACtB,QAAS,GAAGA,CAAW,kBACvB,YAAa,EAAA,EAEjB,CACI,OAAQ,GAAGA,CAAW,qBACtB,QAAS,GAAGA,CAAW,sBACvB,YAAa,EAAA,EAEjB,CACI,OAAQ,GAAGA,CAAW,oBACtB,QAAS,GAAGA,CAAW,qBACvB,YAAa,EAAA,EAEjB,CACI,OAAQ,GAAGA,CAAW,oBACtB,QAAS,GAAGA,CAAW,qBACvB,YAAa,EAAA,EAEjB,CACI,OAAQ,GAAGA,CAAW,sBACtB,QAAS,GAAGA,CAAW,uBACvB,YAAa,EAAA,CACjB,EAKC,IAAI,CAACC,EAAUhe,IAAU,CACtB,MAAMie,EAAMnD,GAAI,MAAM,UAAUkD,EAAS,MAAM,GAAK,GAC9CE,EAAUpD,GAAI,MAAM,UAAUkD,EAAS,OAAO,GAAKA,EAAS,YAGlE,GAAI,CAACC,EAAI,KAAA,GAAU,CAACC,EAAQ,OACxB,MAAO,GAGX,IAAIC,EAAc,GAClB,OAAIne,EAAQoa,GAAgB,cACxB+D,EAAc,sBAEX,8BAA8BF,CAAG,oCAAoCE,CAAW,UAAUD,CAAO,IAC5G,CAAC,EACA,OAAOzH,GAAUA,IAAW,EAAE,EAC9B,KAAK,EAAE,EAGZ,OAAKqH,EAIE;AAAA;AAAA;AAAA;AAAA,0BAIWA,CAAa;AAAA;AAAA;AAAA;AAAA,UAPpB,EAYf,CAKQ,wBAA+B,CACnC,MAAMrC,EAAWkC,GAAuB,WAAW,EAC/ClC,GACA2C,GAAuB3C,CAAQ,CAEvC,CAKQ,mBAA0B,CAC9B,GAAIjB,KAAkB,CAClB,MAAMM,EAAMU,GAAwB,KAAK,EACnC6C,EAAaV,GAAuB,cAAc,EAEpD7C,GACAwD,GAAmBxD,EAAK,CAAE,aAAc,SAAU,EAGlDuD,GACAC,GAAmBD,EAAY,CAC3B,aAAc,OACd,WAAc,EAAA,CACjB,CAET,CACJ,CAKQ,qBAA4B,CAChC,MAAME,EAAiBlD,GAAA,EAEvB,WAAW,IAAM,CACb,GAAI,CAEA,MAAMxX,EAAS,SAAS,iBAAiB,2BAA2B,EAC9D2a,EAAkB3a,EAAO,QAAU0a,EAAe,iBAGlDE,EAAmBF,EAAe,gBAAkBC,EAG1D,IAAIE,EAAiC,GACRH,EAAe,gBAAkB1a,EAAO,QAAUqW,GAAuB,0BAGlGwE,EAAiB,CACb,SAAUH,EAAe,iBACzB,aAAcA,EAAe,kBAC7B,aAAc,GACd,OAAQ,EAAA,GAKhB,MAAMvC,EAAWxB,GAAA,EACXmE,EAAkB,EACxB,IAAIC,EAAe,OACf5C,IACA4C,EAAeD,GAInB,IAAIE,EAA8B,QAC9BJ,IACAI,EAAY,QAGhB,MAAMC,EAAiB,IAAI3F,GAAO,aAAc,CAC5C,QAASyF,EACT,IAAKL,EAAe,IACpB,KAAMM,EACN,SAAUH,EACV,MAAOH,EAAe,gBACtB,KAAMA,EAAe,iBACrB,MAAOnE,GAAgB,aACvB,WAAY,GACZ,OAAQ,EAAA,CACX,EAED0E,EAAe,MAAA,CAUnB,MAAQ,CAER,CACJ,EAAG5E,GAAuB,iBAAiB,CAC/C,CAKQ,yBAAgC,CACpC,GAAI,CAEA,MAAMnhB,EAAQ,IAAI,YAAY,oBAAqB,CAC/C,OAAQ,CACJ,YAAa2hB,GAAc,IAAI,YAC/B,WAAY,QAAA,CAChB,CACH,EACD,SAAS,cAAc3hB,CAAK,CAChC,MAAQ,CAER,CACJ,CACJ,CC1hBO,MAAMgmB,EAAa,CAKd,aAAc,CAHtB,KAAQ,SAA4B,CAAA,EACpC,KAAQ,cAAgB,EAIxB,CAKA,OAAc,aAA4B,CACtC,OAAKA,GAAa,WACdA,GAAa,SAAW,IAAIA,IAEzBA,GAAa,QACxB,CAKO,YAAsB,CACzB,GAAI,CACA,OAAI,KAAK,gBAKT,KAAK,yBAAA,EACL,KAAK,cAAgB,IACd,EACX,MAAQ,CACJ,MAAO,EACX,CACJ,CAKO,WAAoBnmB,EAAyBomB,EAAiC,CACjF,GAAI,CACA,OAAOpmB,EAAA,CACX,OAASqmB,EAAO,CACZ,KAAK,SAASA,EAAgBD,CAAO,EACrC,MACJ,CACJ,CAKO,YAAqBpmB,EAAkComB,EAA0C,CACpG,OAAOpmB,EAAA,EAAW,MAAOqmB,GAAU,CAC/B,KAAK,SAASA,EAAgBD,CAAO,CAEzC,CAAC,CACL,CAKQ,SAASC,EAAcD,EAAuB,CAClD,GAAI,CACA,MAAMjgB,EAAuB,CACzB,cAAe,KACf,MAAAkgB,EACA,QAAAD,CAAA,EAGJ,KAAK,SAAS,KAAKjgB,CAAK,EAGpB,KAAK,SAAS,OAASob,GAAe,uBACtC,KAAK,SAAS,MAAA,CAOtB,MAAQ,CAER,CACJ,CAKQ,0BAAiC,CACrC,GAAI,CAEA,WAAW,iBAAiB,qBAAuBphB,GAAU,CACzD,KAAK,SACD,IAAI,MAAM,OAAOA,EAAM,MAAM,CAAC,EAC9B,6BAAA,CAER,CAAC,CACL,MAAQ,CAER,CACJ,CAKO,aAA+B,CAClC,MAAO,CAAC,GAAG,KAAK,QAAQ,CAC5B,CAKO,eAAsB,CACzB,KAAK,SAAW,CAAA,CACpB,CACJ,CCpHO,MAAMmmB,EAAc,CAGf,aAAc,CAEtB,CAKA,OAAc,aAA6B,CACvC,OAAKA,GAAc,WACfA,GAAc,SAAW,IAAIA,IAE1BA,GAAc,QACzB,CAKO,YAAsB,CACzB,GAAI,CAEA,OADqBpE,GAAI,QAAQ,IAAI,WAAW,IACxB,MAC5B,MAAQ,CAEJ,GAAI,CACA,OAAO,WAAW,SAAS,SAAS,SAAS,OAAO,CACxD,MAAQ,CACJ,MAAO,EACX,CACJ,CACJ,CAKO,WAAkC,CACrC,OAAOJ,EACX,CAKO,cAAwB,CAC3B,GAAI,CAEA,MAAMyE,EAAkB,CAAC,OAAQ,WAAY,UAAW,UAAW,WAAW,EAE9E,UAAWnB,KAAYmB,EAAiB,CACpC,MAAMlB,EAAMnD,GAAI,MAAM,UAAU,GAAGJ,GAAc,IAAI,WAAW,UAAUsD,CAAQ,KAAK,EACjFoB,EAAOtE,GAAI,MAAM,UAAU,GAAGJ,GAAc,IAAI,WAAW,UAAUsD,CAAQ,MAAM,EAEzF,GAAIC,GAAOmB,EACP,MAAO,EAEf,CAEA,MAAO,EACX,MAAQ,CACJ,MAAO,EACX,CACJ,CACJ,CCzDAtE,GAAI,aAAa,IAAIJ,GAAc,IAAI,YAAa,IAAM,CACtD,MAAM2E,EAAeN,GAAa,YAAA,EAC5BO,EAAgBJ,GAAc,YAAA,EAGpC,GAAI,CAACG,EAAa,aACd,OAGJ,MAAME,EAAkB,IAAIhE,GAG5BiE,GAAAA,OAAOC,GAAS,UAAW,WAAY,SAAmCC,EAAiB,CACvFL,EAAa,WAAW,IAAM,CACtBC,EAAc,cAGd,WAAW,IAAM,CACbC,EAAgB,qBAAA,CACpB,EAAG,GAAe,CAE1B,EAAG,6BAA6B,CACpC,CAAC,EAEDC,GAAAA,OAAOC,GAAS,UAAW,WAAY,SAAmCC,EAAiB,CACvFL,EAAa,WAAW,IAAM,CAErB,SAAS,eAAe3E,GAAc,GAAG,cAAc,GAExD,WAAW,IAAM,CACb6E,EAAgB,qBAAA,CACpB,EAAG,GAAe,CAE1B,EAAG,6BAA6B,CACpC,CAAC,CACL,CAAC", "x_google_ignoreList": [0]}