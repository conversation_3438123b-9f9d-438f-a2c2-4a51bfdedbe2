/* TagTiles Extension Styles */

/* Splide Tag Container */
.splideTagContainer {
  width: 100%;
  margin-bottom: 20px;
}

.TagTextOuterContainer {
  width: 100%;
}

.TagTextContainer {
  text-align: center;
  padding: 10px 0;
  font-size: 16px;
  font-weight: bold;
  color: #fff;
}

.TagTextIcon {
  display: inline-block;
  margin-right: 8px;
}

/* Tag Splide Styles */
.tagSplide {
  width: 100%;
  padding: 20px 0;
  overflow: hidden;
  min-height: 200px; /* Increased from 160px to 200px */
  position: relative;
  z-index: 10; /* Ensure splide is above other elements */
}

.tagSplide.is-dragging {
  cursor: grabbing;
}

.tagSplide .splide__track {
  overflow: hidden;
  min-height: 120px; /* Ensure track has height */
}

.tagSplide .splide__list {
  display: flex !important;
  align-items: center;
  transition-timing-function: ease-out;
  min-height: 120px; /* Ensure list has height */
  margin: 0;
  padding: 0;
  list-style: none;
}

.tagSplide .splide__slide {
  text-align: center;
  font-size: 18px;
  background: transparent;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: opacity 0.3s ease, transform 0.3s ease;
}

.tagSplide .splide__slide.is-active {
  opacity: 1;
}

.tagSplide .splide__slide.is-next,
.tagSplide .splide__slide.is-prev {
  opacity: 0.8;
}

.splide__slide-tag {
  width: auto;
  min-width: 200px; /* More reasonable increase from 150px to 200px */
  height: 160px; /* Increased from 120px to 160px */
  border-radius: 8px;
  overflow: hidden;
  position: relative;
  flex-shrink: 0;
  display: flex !important; /* Force display */
  visibility: visible !important; /* Force visibility */
}

.splide__slide-tag a {
  display: block;
  width: 100%;
  height: 100%;
  text-decoration: none;
}

.splide__slide-tag-inner {
  width: 100%;
  height: 100%;
  min-height: 160px; /* Match the increased slide height */
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  position: relative;
  overflow: hidden;
}

.splide__slide-tag-inner:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.splide__slide-tag-inner::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.splide__slide-tag-inner:hover::before {
  transform: translateX(100%);
}

.splide__slide-tag-inner-mobile {
  width: 100%;
  height: 100%;
  min-height: 120px; /* Increased from 90px to 120px */
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  font-size: 12px;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  position: relative;
  overflow: hidden;
}

/* Social Media Buttons */
.Button--primary {
  border-radius: 2rem !important;
}

.Button-label img {
  width: 32px;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.Button-label img:hover {
  transform: scale(1.1);
}

/* Autoplay pause indicator */
.tagSplide.is-paused {
  opacity: 0.9;
}

.tagSplide.is-paused .splide__slide {
  animation-play-state: paused;
}

/* Loop mode styles */
.tagSplide[data-splide-type="loop"] .splide__slide {
  opacity: 1;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .splideTagContainer {
    margin: 0 -15px;
  }

  .tagSplide {
    padding: 10px 0;
    min-height: 160px; /* Increased from 120px to 160px */
  }

  .splide__slide-tag {
    height: 140px; /* Increased from 100px to 140px */
    min-width: 160px; /* More reasonable increase from 120px to 160px */
  }

  .splide__slide-tag-inner {
    min-height: 140px; /* Increased from 100px to 140px */
  }

  .TagTextContainer {
    font-size: 14px;
    padding: 8px 0;
  }

  /* Disable hover effects on mobile */
  .splide__slide-tag-inner:hover {
    transform: none;
    box-shadow: none;
  }

  .splide__slide-tag-inner:hover::before {
    transform: translateX(-100%);
  }
}

/* Splide Navigation Arrows Styling */
.tagSplide .splide__arrows {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 20;
}

.tagSplide .splide__arrow {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 44px;
  height: 44px;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.9) 100%);
  border: 2px solid rgba(255, 255, 255, 0.3);
  box-shadow:
    0 4px 12px rgba(0, 0, 0, 0.15),
    0 2px 4px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  cursor: pointer;
  pointer-events: auto;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
}

.tagSplide .splide__arrow::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.6) 0%, transparent 50%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.tagSplide .splide__arrow:hover {
  background: linear-gradient(135deg, rgba(255, 255, 255, 1) 0%, rgba(248, 250, 252, 0.95) 100%);
  border-color: rgba(59, 130, 246, 0.3);
  box-shadow:
    0 8px 25px rgba(0, 0, 0, 0.2),
    0 4px 10px rgba(59, 130, 246, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);
  transform: translateY(-50%) scale(1.05);
}

.tagSplide .splide__arrow:hover::before {
  opacity: 1;
}

.tagSplide .splide__arrow:active {
  transform: translateY(-50%) scale(0.95);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.tagSplide .splide__arrow--prev {
  left: 10px;
}

.tagSplide .splide__arrow--next {
  right: 10px;
}

.tagSplide .splide__arrow svg {
  width: 20px;
  height: 20px;
  fill: #333;
  transition: fill 0.3s ease;
}

.tagSplide .splide__arrow:hover svg {
  fill: #000;
}

.tagSplide .splide__arrow--prev svg {
  transform: rotate(180deg);
}

/* Pulse animation for initial hint */
@keyframes arrowPulse {
  0%, 100% {
    transform: translateY(-50%) scale(1);
    box-shadow:
      0 4px 12px rgba(0, 0, 0, 0.15),
      0 2px 4px rgba(0, 0, 0, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.8);
  }
  50% {
    transform: translateY(-50%) scale(1.05);
    box-shadow:
      0 6px 18px rgba(0, 0, 0, 0.2),
      0 3px 6px rgba(59, 130, 246, 0.15),
      inset 0 1px 0 rgba(255, 255, 255, 0.9);
  }
}

/* Arrow visibility on hover */
.tagSplide .splide__arrows {
  opacity: 0;
  transition: opacity 0.3s ease;
}

.tagSplide:hover .splide__arrows {
  opacity: 1;
}

/* Initial pulse hint - shows for first few seconds */
.tagSplide .splide__arrow {
  animation: arrowPulse 2s ease-in-out 3;
  animation-delay: 1s;
}

.tagSplide:hover .splide__arrow {
  animation: none;
}

/* Additional mobile fixes */
@media (max-width: 480px) {
  .splide__slide-tag {
    height: 120px; /* Increased from 90px to 120px */
    min-width: 130px; /* More reasonable increase from 100px to 130px */
  }

  .splide__slide-tag-inner {
    min-height: 120px; /* Increased from 90px to 120px */
  }

  /* Mobile arrow adjustments */
  .tagSplide .splide__arrow {
    width: 36px;
    height: 36px;
    background: rgba(255, 255, 255, 0.95);
  }

  .tagSplide .splide__arrow--prev {
    left: 5px;
  }

  .tagSplide .splide__arrow--next {
    right: 5px;
  }

  .tagSplide .splide__arrow svg {
    width: 16px;
    height: 16px;
  }

  /* Always show arrows on mobile */
  .tagSplide .splide__arrows {
    opacity: 1;
  }
}
